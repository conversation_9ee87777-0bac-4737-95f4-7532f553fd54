-- Enhanced Delivery Status Schema (CORRECTED VERSION)
-- Adds comprehensive delivery status tracking with audit trail
-- Fixed to use 'user_type' instead of 'role' column

-- Step 1: Add delivery status column with proper constraints
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status VARCHAR(20) DEFAULT 'not_assigned' 
CHECK (delivery_status IN ('not_assigned', 'assigned', 'picked', 'out_for_delivery', 'delivered', 'failed', 'returned'));

-- Step 2: Add audit trail columns for delivery status changes
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status_updated_by UUID REFERENCES users(id);
<PERSON>TE<PERSON> TABLE orders ADD COLUMN IF NOT EXISTS delivery_status_updated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status_history JSONB DEFAULT '[]'::jsonb;

-- Step 3: Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_delivery_status ON orders(delivery_status);
CREATE INDEX IF NOT EXISTS idx_orders_delivery_person ON orders(assigned_delivery_person);
CREATE INDEX IF NOT EXISTS idx_orders_delivery_status_updated ON orders(delivery_status_updated_at);

-- Step 4: Create delivery status change log table for detailed audit trail
CREATE TABLE IF NOT EXISTS delivery_status_changes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    changed_by UUID REFERENCES users(id),
    changed_by_name VARCHAR(255),
    changed_by_role VARCHAR(50),
    change_reason TEXT,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Step 5: Add indexes for delivery status changes table
CREATE INDEX IF NOT EXISTS idx_delivery_status_changes_order ON delivery_status_changes(order_id);
CREATE INDEX IF NOT EXISTS idx_delivery_status_changes_date ON delivery_status_changes(changed_at);
CREATE INDEX IF NOT EXISTS idx_delivery_status_changes_user ON delivery_status_changes(changed_by);

-- Step 6: Create function to automatically update delivery status history
CREATE OR REPLACE FUNCTION update_delivery_status_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if delivery_status actually changed
    IF OLD.delivery_status IS DISTINCT FROM NEW.delivery_status THEN
        -- Update the history JSONB array
        NEW.delivery_status_history = COALESCE(OLD.delivery_status_history, '[]'::jsonb) || 
            jsonb_build_object(
                'old_status', OLD.delivery_status,
                'new_status', NEW.delivery_status,
                'changed_at', NOW(),
                'changed_by', NEW.delivery_status_updated_by
            );
        
        -- Set the updated timestamp
        NEW.delivery_status_updated_at = NOW();
        
        -- Insert into detailed log table (CORRECTED: using user_type instead of role)
        INSERT INTO delivery_status_changes (
            order_id, 
            old_status, 
            new_status, 
            changed_by,
            changed_by_name,
            changed_by_role
        ) VALUES (
            NEW.id,
            OLD.delivery_status,
            NEW.delivery_status,
            NEW.delivery_status_updated_by,
            (SELECT full_name FROM users WHERE id = NEW.delivery_status_updated_by),
            (SELECT user_type FROM users WHERE id = NEW.delivery_status_updated_by)
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create trigger for automatic history tracking
DROP TRIGGER IF EXISTS trigger_delivery_status_history ON orders;
CREATE TRIGGER trigger_delivery_status_history
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_delivery_status_history();

-- Step 8: Initialize delivery_status for existing orders based on current state
UPDATE orders 
SET 
    delivery_status = CASE 
        WHEN assigned_delivery_person IS NOT NULL AND status IN ('delivered') THEN 'delivered'
        WHEN assigned_delivery_person IS NOT NULL AND status IN ('shipped', 'out_for_delivery') THEN 'out_for_delivery'
        WHEN assigned_delivery_person IS NOT NULL AND status IN ('assigned', 'confirmed', 'preparing', 'ready') THEN 'assigned'
        ELSE 'not_assigned'
    END,
    delivery_status_updated_at = NOW(),
    delivery_status_updated_by = assigned_delivery_person
WHERE delivery_status = 'not_assigned' OR delivery_status IS NULL;

-- Step 9: Create view for easy delivery status reporting (CORRECTED: using user_type)
CREATE OR REPLACE VIEW delivery_status_summary AS
SELECT 
    o.id,
    o.order_number,
    o.status as order_status,
    o.delivery_status,
    o.assigned_delivery_person,
    o.delivery_person_name,
    o.delivery_status_updated_at,
    o.delivery_status_updated_by,
    u.full_name as status_updated_by_name,
    u.user_type as status_updated_by_role,
    o.created_at,
    o.total,
    customer.full_name as customer_name
FROM orders o
LEFT JOIN users u ON o.delivery_status_updated_by = u.id
LEFT JOIN users customer ON o.customer_id = customer.id
ORDER BY o.created_at DESC;

-- Step 10: Add RLS policies for delivery status changes (CORRECTED: using user_type)
ALTER TABLE delivery_status_changes ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Users can view delivery status changes for their orders" ON delivery_status_changes;
DROP POLICY IF EXISTS "Authorized users can insert delivery status changes" ON delivery_status_changes;

CREATE POLICY "Users can view delivery status changes for their orders" ON delivery_status_changes
    FOR SELECT USING (
        auth.uid() IN (
            SELECT customer_id FROM orders WHERE id = order_id
            UNION
            SELECT assigned_delivery_person FROM orders WHERE id = order_id
        )
        OR
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager', 'store_manager')
        )
    );

CREATE POLICY "Authorized users can insert delivery status changes" ON delivery_status_changes
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager', 'store_manager', 'delivery_person')
        )
    );

-- Step 11: Verification queries
SELECT 'Enhanced Delivery Status Schema Successfully Applied!' as status;

-- Show updated orders table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name LIKE '%delivery%'
ORDER BY ordinal_position;

-- Show delivery status distribution
SELECT 
    delivery_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM orders 
GROUP BY delivery_status
ORDER BY count DESC;

-- Show recent delivery status changes (if any)
SELECT 
    COALESCE(o.order_number, 'N/A') as order_number,
    COALESCE(dsc.old_status, 'N/A') as old_status,
    COALESCE(dsc.new_status, 'N/A') as new_status,
    COALESCE(dsc.changed_by_name, 'System') as changed_by_name,
    COALESCE(dsc.changed_by_role, 'system') as changed_by_role,
    COALESCE(dsc.changed_at::text, 'N/A') as changed_at
FROM delivery_status_changes dsc
JOIN orders o ON dsc.order_id = o.id
ORDER BY dsc.changed_at DESC
LIMIT 10;
