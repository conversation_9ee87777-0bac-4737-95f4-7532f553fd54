-- Fix Existing Order Assignments
-- Run this in Supabase SQL Editor to fix orders that were assigned but don't have status="assigned"

-- Step 1: Check current state of assignments for the specific delivery person
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name,
  delivery_assigned_at
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY delivery_assigned_at DESC;

-- Step 2: Update orders that have assigned_delivery_person but wrong status
-- This fixes orders that were assigned with the old logic
-- IMPORTANT: Only update orders that are in early stages (not completed/delivered)
UPDATE orders
SET
  status = 'assigned',
  updated_at = NOW()
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('pending', 'confirmed', 'preparing', 'ready', 'shipped')
  AND status NOT IN ('assigned', 'picked', 'out_for_delivery', 'delivered', 'completed', 'cancelled', 'returned');

-- Step 3: Verify the fix worked
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name,
  delivery_assigned_at,
  updated_at
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY delivery_assigned_at DESC;

-- Step 4: Count orders by status for this delivery person
SELECT 
  status,
  COUNT(*) as count
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
GROUP BY status
ORDER BY count DESC;

-- Expected result after fix:
-- status = 'assigned', count = 3 (or however many orders were assigned)
