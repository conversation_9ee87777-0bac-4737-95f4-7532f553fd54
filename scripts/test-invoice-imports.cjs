#!/usr/bin/env node

/**
 * Quick test to verify invoice component imports are correct
 */

const { readFileSync } = require('fs');
const { join } = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkImports() {
  log('🔍 Testing Invoice Component Imports...', colors.blue);
  log('');

  const files = [
    'src/components/orders/Invoice.tsx',
    'src/components/invoices/InvoiceGenerator.tsx'
  ];

  let allCorrect = true;

  files.forEach(filePath => {
    try {
      const content = readFileSync(join(process.cwd(), filePath), 'utf8');
      
      log(`Checking ${filePath}:`, colors.yellow);
      
      // Check for correct import
      if (content.includes("import { useAuth } from '../../contexts/AuthContext'")) {
        log('  ✅ Correct useAuth import found', colors.green);
      } else {
        log('  ❌ useAuth import not found', colors.red);
        allCorrect = false;
      }
      
      // Check for correct usage
      if (content.includes('const { user } = useAuth()')) {
        log('  ✅ Correct useAuth usage found', colors.green);
      } else {
        log('  ❌ useAuth usage not found', colors.red);
        allCorrect = false;
      }
      
      // Check for incorrect imports
      if (content.includes("import { AuthContext }") || content.includes("useContext(AuthContext)")) {
        log('  ❌ Found incorrect AuthContext import/usage', colors.red);
        allCorrect = false;
      } else {
        log('  ✅ No incorrect AuthContext usage found', colors.green);
      }
      
      log('');
    } catch (error) {
      log(`  ❌ Error reading ${filePath}: ${error.message}`, colors.red);
      allCorrect = false;
    }
  });

  if (allCorrect) {
    log('🎉 All invoice component imports are correct!', colors.green);
    log('');
    log('✅ The SyntaxError should now be resolved.', colors.green);
    log('✅ Invoice components will properly access authenticated user data.', colors.green);
  } else {
    log('❌ Some import issues found. Please review the files.', colors.red);
    process.exit(1);
  }
}

checkImports();
