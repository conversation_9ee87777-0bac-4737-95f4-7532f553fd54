#!/usr/bin/env node

/**
 * Validation script for Database Field Mapping Fix
 * Tests that camelCase fields are properly mapped to snake_case database columns
 */

const { readFileSync } = require('fs');
const { join } = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function validateFileContains(filePath, searchTerms) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const found = [];
    const missing = [];
    
    searchTerms.forEach(term => {
      if (content.includes(term)) {
        found.push(term);
      } else {
        missing.push(term);
      }
    });
    
    return { found, missing };
  } catch (error) {
    return { found: [], missing: searchTerms };
  }
}

async function validateFieldMappingFix() {
  log('🔍 Validating Database Field Mapping Fix...', colors.blue);
  log('');

  let allTestsPassed = true;

  // Test 1: DataSync Service Field Mapping
  log('1. Checking DataSync Service Field Mapping...', colors.yellow);
  const dataSyncPath = join(process.cwd(), 'src/services/dataSync.ts');
  
  const dataSyncRequiredTerms = [
    'case \'resellerPrice\':',
    'dbUpdates.reseller_price = productUpdates[key];',
    'dbFields.reseller_price = productFields[key];',
    'case \'minStock\':',
    'dbUpdates.min_stock = productUpdates[key];',
    'case \'categoryId\':',
    'dbUpdates.category_id = productUpdates[key];',
    'case \'featuredImage\':',
    'dbUpdates.featured_image = productUpdates[key];',
    'Object.keys(productUpdates).forEach(key => {',
    'Object.keys(productFields).forEach(key => {'
  ];
  
  const dataSyncValidation = validateFileContains(dataSyncPath, dataSyncRequiredTerms);
  dataSyncValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  dataSyncValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 2: Database Schema Validation
  log('2. Checking Database Schema...', colors.yellow);
  const typesPath = join(process.cwd(), 'src/integrations/supabase/types.ts');
  
  const schemaRequiredTerms = [
    'reseller_price: number | null',
    'min_stock: number | null',
    'category_id: string | null',
    'featured_image: string | null',
    'thumbnail_images: string[] | null',
    'is_active: boolean | null',
    'is_featured: boolean | null'
  ];
  
  const schemaValidation = validateFileContains(typesPath, schemaRequiredTerms);
  schemaValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  schemaValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 3: Field Mapping Logic Validation
  log('3. Validating Field Mapping Logic...', colors.yellow);
  
  // Test the mapping logic
  const testMappings = [
    { input: 'resellerPrice', expected: 'reseller_price' },
    { input: 'minStock', expected: 'min_stock' },
    { input: 'maxStock', expected: 'max_stock' },
    { input: 'costPrice', expected: 'cost_price' },
    { input: 'categoryId', expected: 'category_id' },
    { input: 'featuredImage', expected: 'featured_image' },
    { input: 'thumbnailImages', expected: 'thumbnail_images' },
    { input: 'isActive', expected: 'is_active' },
    { input: 'isFeatured', expected: 'is_featured' },
    { input: 'isNew', expected: 'is_new' },
    { input: 'reviewCount', expected: 'review_count' },
    { input: 'reservedStock', expected: 'reserved_stock' }
  ];

  // Simulate the mapping function
  function mapFieldName(camelCaseField) {
    switch (camelCaseField) {
      case 'resellerPrice': return 'reseller_price';
      case 'minStock': return 'min_stock';
      case 'maxStock': return 'max_stock';
      case 'costPrice': return 'cost_price';
      case 'categoryId': return 'category_id';
      case 'featuredImage': return 'featured_image';
      case 'thumbnailImages': return 'thumbnail_images';
      case 'isActive': return 'is_active';
      case 'isFeatured': return 'is_featured';
      case 'isNew': return 'is_new';
      case 'reviewCount': return 'review_count';
      case 'reservedStock': return 'reserved_stock';
      default: return camelCaseField;
    }
  }

  let mappingCorrect = true;
  testMappings.forEach(({ input, expected }) => {
    const result = mapFieldName(input);
    if (result === expected) {
      log(`   ✅ ${input} -> ${expected}`, colors.green);
    } else {
      log(`   ❌ ${input} -> ${result} (expected: ${expected})`, colors.red);
      mappingCorrect = false;
      allTestsPassed = false;
    }
  });

  if (mappingCorrect) {
    log(`   ✅ All field mappings are correct`, colors.green);
  }

  // Test 4: Product Update Data Flow
  log('4. Testing Product Update Data Flow...', colors.yellow);
  
  // Simulate a product update with reseller price
  const mockProductUpdate = {
    title: 'Test Product',
    price: 100,
    resellerPrice: 80,
    minStock: 10,
    categoryId: 'cat-123',
    featuredImage: 'image.jpg',
    isActive: true
  };

  // Simulate the mapping process
  const mappedUpdate = {};
  Object.keys(mockProductUpdate).forEach(key => {
    const dbField = mapFieldName(key);
    mappedUpdate[dbField] = mockProductUpdate[key];
  });

  // Check that the mapping worked correctly
  const expectedMappedUpdate = {
    title: 'Test Product',
    price: 100,
    reseller_price: 80,
    min_stock: 10,
    category_id: 'cat-123',
    featured_image: 'image.jpg',
    is_active: true
  };

  const mappingMatches = JSON.stringify(mappedUpdate) === JSON.stringify(expectedMappedUpdate);
  if (mappingMatches) {
    log(`   ✅ Product update data flow mapping correct`, colors.green);
  } else {
    log(`   ❌ Product update data flow mapping error`, colors.red);
    log(`   Expected: ${JSON.stringify(expectedMappedUpdate)}`, colors.red);
    log(`   Got: ${JSON.stringify(mappedUpdate)}`, colors.red);
    allTestsPassed = false;
  }

  // Final result
  log('');
  if (allTestsPassed) {
    log('🎉 All database field mapping fixes validated successfully!', colors.green);
    log('');
    log('✨ Fixes Applied:', colors.blue);
    log('   • Added proper field mapping in dataSync.ts createProduct', colors.reset);
    log('   • Added proper field mapping in dataSync.ts updateProduct', colors.reset);
    log('   • CamelCase frontend fields now map to snake_case database columns', colors.reset);
    log('   • Comprehensive mapping for all product fields', colors.reset);
    log('');
    log('✨ Key Field Mappings:', colors.blue);
    log('   • resellerPrice -> reseller_price', colors.reset);
    log('   • minStock -> min_stock', colors.reset);
    log('   • categoryId -> category_id', colors.reset);
    log('   • featuredImage -> featured_image', colors.reset);
    log('   • isActive -> is_active', colors.reset);
    log('');
    log('🔧 Error Resolution:', colors.blue);
    log('   • Fixed: "Could not find the \'resellerPrice\' column" error', colors.reset);
    log('   • Fixed: Database schema mismatch issues', colors.reset);
    log('   • Fixed: Product creation and update operations', colors.reset);
    log('');
    log('📋 Next Steps:', colors.yellow);
    log('   • Test reseller price updates in admin dashboard', colors.reset);
    log('   • Verify that changes are saved to database correctly', colors.reset);
    log('   • Check that real-time synchronization works', colors.reset);
  } else {
    log('❌ Some validations failed. Please review the implementation.', colors.red);
    process.exit(1);
  }
}

// Run validation
validateFieldMappingFix().catch(error => {
  log(`Error during validation: ${error.message}`, colors.red);
  process.exit(1);
});
