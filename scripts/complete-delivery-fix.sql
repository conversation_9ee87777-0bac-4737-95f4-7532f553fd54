-- Complete Delivery Assignment Fix
-- This script fixes the database constraint and then updates the assignments

-- PART 1: Fix the database constraint to allow delivery statuses
-- ================================================================

-- Drop the existing status check constraint
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_status_check;

-- Add new constraint that includes delivery statuses
ALTER TABLE orders ADD CONSTRAINT orders_status_check 
CHECK (status IN (
  'pending', 
  'confirmed', 
  'preparing', 
  'ready', 
  'assigned',         -- For delivery assignment
  'picked',           -- For picked up by delivery person
  'out_for_delivery', -- For in transit
  'shipped', 
  'delivered', 
  'cancelled', 
  'returned'
));

-- PART 2: Update the liveDataService assignment logic in the database
-- ===================================================================

-- Now we can safely update orders to 'assigned' status
UPDATE orders 
SET 
  status = 'assigned',
  updated_at = NOW()
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('pending', 'confirmed', 'preparing', 'ready', 'shipped')
  AND status NOT IN ('delivered', 'cancelled', 'returned');

-- PART 3: Verification and Results
-- ================================

-- Show the updated constraint
SELECT 
  'Updated Constraint' as info_type,
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass 
  AND conname = 'orders_status_check';

-- Show orders that will now appear in delivery dashboard
SELECT 
  'Orders for Delivery Dashboard' as result_type,
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name,
  delivery_assigned_at,
  updated_at
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('assigned', 'picked', 'out_for_delivery')
ORDER BY delivery_assigned_at DESC;

-- Count orders by status for this delivery person
SELECT 
  'Status Summary' as summary_type,
  status,
  COUNT(*) as count
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
GROUP BY status
ORDER BY count DESC;

-- Final verification - simulate the delivery dashboard query
SELECT 
  'Delivery Dashboard Query Result' as query_type,
  COUNT(*) as orders_that_will_appear,
  'These orders will appear in the Assigned Orders tab' as explanation
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('assigned', 'picked', 'out_for_delivery');

-- Show delivery history orders
SELECT 
  'Delivery History Query Result' as history_type,
  COUNT(*) as completed_orders,
  'These orders will appear in the Delivery History tab' as explanation
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('delivered', 'cancelled', 'returned');

-- Success message
SELECT 
  'SUCCESS' as status,
  'Database constraint updated and orders fixed!' as message,
  'The delivery dashboard should now work correctly.' as next_step;
