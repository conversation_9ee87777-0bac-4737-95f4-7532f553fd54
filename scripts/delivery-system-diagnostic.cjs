#!/usr/bin/env node

/**
 * Comprehensive Delivery System Diagnostic
 * Identifies all issues in the delivery person dashboard system
 */

const { readFileSync } = require('fs');
const { join } = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function validateFileContains(filePath, searchTerms) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const found = [];
    const missing = [];
    
    searchTerms.forEach(term => {
      if (content.includes(term)) {
        found.push(term);
      } else {
        missing.push(term);
      }
    });
    
    return { found, missing };
  } catch (error) {
    return { found: [], missing: searchTerms };
  }
}

async function comprehensiveDeliverySystemDiagnostic() {
  log('🔍 COMPREHENSIVE DELIVERY SYSTEM DIAGNOSTIC', colors.blue);
  log('=' .repeat(60), colors.blue);
  log('');

  let criticalIssues = [];
  let warnings = [];
  let recommendations = [];

  // ISSUE 1: Database Schema Analysis
  log('1. 🗄️  DATABASE SCHEMA ANALYSIS', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  
  const typesPath = join(process.cwd(), 'src/integrations/supabase/types.ts');
  const schemaDeliveryFields = [
    'assigned_delivery_person',
    'assigned_delivery_person_id', 
    'delivery_person_name',
    'delivery_assigned_at',
    'delivered_at'
  ];
  
  const schemaValidation = validateFileContains(typesPath, schemaDeliveryFields);
  
  if (schemaValidation.missing.length > 0) {
    criticalIssues.push('❌ CRITICAL: Database schema missing delivery fields');
    schemaValidation.missing.forEach(field => {
      log(`   ❌ Missing database field: ${field}`, colors.red);
    });
    log('   💡 Solution: Run database migration to add delivery fields', colors.cyan);
  } else {
    log('   ✅ All delivery fields exist in database schema', colors.green);
  }

  // ISSUE 2: Service Layer Analysis
  log('');
  log('2. 🔧 SERVICE LAYER ANALYSIS', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  
  const deliveryServicePath = join(process.cwd(), 'src/services/deliveryService.ts');
  const serviceIssues = [];
  
  // Check if service uses correct field names
  const correctFieldUsage = validateFileContains(deliveryServicePath, [
    '.eq(\'assigned_delivery_person\', deliveryPersonId)',
    'assigned_delivery_person,'
  ]);
  
  if (correctFieldUsage.found.length === 2) {
    log('   ✅ Delivery service uses correct field names', colors.green);
  } else {
    serviceIssues.push('Field name mismatch in delivery service');
    log('   ❌ Delivery service field name issues', colors.red);
  }
  
  // Check error handling
  const errorHandling = validateFileContains(deliveryServicePath, [
    'console.error(',
    'try {',
    'catch (error)'
  ]);
  
  if (errorHandling.found.length >= 3) {
    log('   ✅ Proper error handling implemented', colors.green);
  } else {
    warnings.push('Insufficient error handling in delivery service');
    log('   ⚠️  Limited error handling', colors.yellow);
  }

  // ISSUE 3: Hook Integration Analysis
  log('');
  log('3. 🪝 HOOK INTEGRATION ANALYSIS', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  
  const hooksPath = join(process.cwd(), 'src/hooks/useDeliveryData.ts');
  const hookFeatures = [
    'useDeliveryOrders',
    'useDeliveryHistory', 
    'useDeliveryStats',
    'useDeliveryActions',
    'updateOrderStatus'
  ];
  
  const hooksValidation = validateFileContains(hooksPath, hookFeatures);
  
  if (hooksValidation.missing.length === 0) {
    log('   ✅ All delivery hooks implemented', colors.green);
  } else {
    criticalIssues.push('Missing delivery hooks');
    hooksValidation.missing.forEach(hook => {
      log(`   ❌ Missing hook: ${hook}`, colors.red);
    });
  }

  // ISSUE 4: Dashboard Component Analysis
  log('');
  log('4. 📊 DASHBOARD COMPONENT ANALYSIS', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  
  const dashboardPath = join(process.cwd(), 'src/components/dashboards/DeliveryDashboard.tsx');
  const dashboardFeatures = [
    'useDeliveryOrders(user.id)',
    'useDeliveryHistory(user.id)',
    'useDeliveryStats(user.id)',
    'handleUpdateOrderStatus',
    'handlePhoneCall',
    'handleViewMap'
  ];
  
  const dashboardValidation = validateFileContains(dashboardPath, dashboardFeatures);
  
  if (dashboardValidation.missing.length === 0) {
    log('   ✅ All dashboard features implemented', colors.green);
  } else {
    criticalIssues.push('Missing dashboard functionality');
    dashboardValidation.missing.forEach(feature => {
      log(`   ❌ Missing feature: ${feature}`, colors.red);
    });
  }

  // ISSUE 5: Real-time Synchronization Analysis
  log('');
  log('5. 🔄 REAL-TIME SYNCHRONIZATION ANALYSIS', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  
  const realTimeServicePath = join(process.cwd(), 'src/services/realTimeService.ts');
  const syncFeatures = [
    'syncDeliveryStatusUpdate',
    'syncDeliveryCompleted',
    'syncDeliveryAssigned'
  ];
  
  const syncValidation = validateFileContains(realTimeServicePath, syncFeatures);
  
  if (syncValidation.missing.length === 0) {
    log('   ✅ All sync functions implemented', colors.green);
  } else {
    criticalIssues.push('Missing real-time sync functions');
    syncValidation.missing.forEach(func => {
      log(`   ❌ Missing sync function: ${func}`, colors.red);
    });
  }

  // ISSUE 6: Assignment Process Analysis
  log('');
  log('6. 📋 ASSIGNMENT PROCESS ANALYSIS', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  
  const assignmentModalPath = join(process.cwd(), 'src/components/orders/DeliveryAssignmentModal.tsx');
  const liveDataServicePath = join(process.cwd(), 'src/services/liveDataService.ts');
  
  const assignmentFeatures = [
    'updateOrderDeliveryAssignment',
    'assigned_delivery_person: deliveryPersonId',
    'delivery_person_name:'
  ];
  
  const assignmentValidation = validateFileContains(liveDataServicePath, assignmentFeatures);
  
  if (assignmentValidation.missing.length === 0) {
    log('   ✅ Assignment process implemented', colors.green);
  } else {
    criticalIssues.push('Assignment process issues');
    assignmentValidation.missing.forEach(feature => {
      log(`   ❌ Missing assignment feature: ${feature}`, colors.red);
    });
  }

  // SUMMARY AND RECOMMENDATIONS
  log('');
  log('📋 DIAGNOSTIC SUMMARY', colors.magenta);
  log('=' .repeat(60), colors.magenta);
  
  log(`🔴 Critical Issues: ${criticalIssues.length}`, colors.red);
  criticalIssues.forEach(issue => log(`   • ${issue}`, colors.red));
  
  log(`🟡 Warnings: ${warnings.length}`, colors.yellow);
  warnings.forEach(warning => log(`   • ${warning}`, colors.yellow));
  
  log('');
  log('🎯 ROOT CAUSE ANALYSIS', colors.cyan);
  log('-'.repeat(30), colors.cyan);
  
  if (schemaValidation.missing.length > 0) {
    log('PRIMARY ISSUE: Database schema missing delivery assignment fields', colors.red);
    log('');
    log('IMPACT:', colors.yellow);
    log('• Assignment process tries to set non-existent fields', colors.reset);
    log('• Delivery service queries non-existent fields', colors.reset);
    log('• Dashboard shows "No assigned orders" even when orders are assigned', colors.reset);
    log('• Real-time sync fails due to missing data', colors.reset);
    log('');
    log('IMMEDIATE SOLUTION:', colors.green);
    log('1. Run database migration to add delivery fields', colors.reset);
    log('2. Update database schema types', colors.reset);
    log('3. Test assignment and dashboard functionality', colors.reset);
  } else {
    log('Database schema appears correct - investigating other issues...', colors.green);
  }
  
  log('');
  log('🚀 RECOMMENDED ACTIONS', colors.green);
  log('-'.repeat(30), colors.green);
  log('1. Execute database migration (scripts/add-delivery-fields-migration.sql)', colors.reset);
  log('2. Regenerate Supabase types', colors.reset);
  log('3. Test order assignment process', colors.reset);
  log('4. Verify delivery dashboard functionality', colors.reset);
  log('5. Test cross-dashboard synchronization', colors.reset);
  
  return {
    criticalIssues: criticalIssues.length,
    warnings: warnings.length,
    needsDatabaseMigration: schemaValidation.missing.length > 0
  };
}

// Run diagnostic
comprehensiveDeliverySystemDiagnostic().then(result => {
  console.log('');
  if (result.criticalIssues > 0) {
    log('🚨 CRITICAL ISSUES FOUND - IMMEDIATE ACTION REQUIRED', colors.red);
    process.exit(1);
  } else if (result.warnings > 0) {
    log('⚠️  WARNINGS FOUND - REVIEW RECOMMENDED', colors.yellow);
  } else {
    log('✅ ALL SYSTEMS OPERATIONAL', colors.green);
  }
}).catch(error => {
  log(`Error during diagnostic: ${error.message}`, colors.red);
  process.exit(1);
});
