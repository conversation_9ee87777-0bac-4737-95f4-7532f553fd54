-- Smart Delivery Assignment Fix
-- This script intelligently handles different order statuses

-- Step 1: Analyze current assignments for the delivery person
SELECT 
  'Current Assignment Analysis' as analysis_type,
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name,
  delivery_assigned_at,
  created_at
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY delivery_assigned_at DESC;

-- Step 2: Show what the delivery dashboard query is looking for
SELECT 
  'Orders Dashboard Query Would Find' as query_type,
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('assigned', 'picked', 'out_for_delivery')
ORDER BY created_at;

-- Step 3: Identify orders that need status correction
SELECT 
  'Orders Needing Status Fix' as fix_type,
  id,
  order_number,
  status as current_status,
  'assigned' as should_be_status,
  assigned_delivery_person,
  delivery_person_name,
  CASE 
    WHEN status IN ('completed', 'delivered', 'cancelled', 'returned') THEN 'CANNOT_FIX - Order already completed'
    WHEN status IN ('pending', 'confirmed', 'preparing', 'ready', 'shipped') THEN 'CAN_FIX - Safe to change to assigned'
    WHEN status IN ('assigned', 'picked', 'out_for_delivery') THEN 'NO_FIX_NEEDED - Already correct'
    ELSE 'UNKNOWN_STATUS'
  END as fix_action
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY created_at;

-- Step 4: Safe update - only fix orders that can be safely changed
UPDATE orders 
SET 
  status = 'assigned',
  updated_at = NOW()
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('pending', 'confirmed', 'preparing', 'ready', 'shipped')
  AND status NOT IN ('assigned', 'picked', 'out_for_delivery', 'delivered', 'completed', 'cancelled', 'returned');

-- Step 5: Show results after fix
SELECT 
  'After Fix - Dashboard Query Results' as result_type,
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name,
  updated_at
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('assigned', 'picked', 'out_for_delivery')
ORDER BY created_at;

-- Step 6: Summary of all orders for this delivery person
SELECT 
  'Final Status Summary' as summary_type,
  status,
  COUNT(*) as count,
  STRING_AGG(order_number, ', ') as order_numbers
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
GROUP BY status
ORDER BY count DESC;

-- Step 7: Instructions for completed orders
SELECT 
  'Instructions for Completed Orders' as instruction_type,
  'If you have orders with status=completed that should appear in delivery dashboard,' as message1,
  'you need to create NEW orders and assign them to the delivery person.' as message2,
  'Completed orders cannot be changed back to assigned status.' as message3;

-- Step 8: Verification query - this is what the delivery dashboard runs
SELECT 
  'Delivery Dashboard Query Simulation' as query_type,
  COUNT(*) as orders_found,
  'This is how many orders the delivery dashboard will show' as explanation
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
  AND status IN ('assigned', 'picked', 'out_for_delivery');
