-- Enhanced Row Level Security (RLS) Policies for YalaOffice
-- This script implements comprehensive server-side authorization

-- =============================================
-- SECURITY FUNCTIONS
-- =============================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT user_type 
    FROM users 
    WHERE id = auth.uid() 
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role() = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is manager or admin
CREATE OR REPLACE FUNCTION is_manager_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role() IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can manage clients
CREATE OR REPLACE FUNCTION can_manage_clients()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role() IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- DROP EXISTING POLICIES
-- =============================================

-- Users table policies
DROP POLICY IF EXISTS users_select_policy ON users;
DROP POLICY IF EXISTS users_insert_policy ON users;
DROP POLICY IF EXISTS users_update_policy ON users;
DROP POLICY IF EXISTS users_delete_policy ON users;

-- Orders table policies
DROP POLICY IF EXISTS orders_select_policy ON orders;
DROP POLICY IF EXISTS orders_insert_policy ON orders;
DROP POLICY IF EXISTS orders_update_policy ON orders;
DROP POLICY IF EXISTS orders_delete_policy ON orders;

-- Products table policies
DROP POLICY IF EXISTS products_select_policy ON products;
DROP POLICY IF EXISTS products_insert_policy ON products;
DROP POLICY IF EXISTS products_update_policy ON products;
DROP POLICY IF EXISTS products_delete_policy ON products;

-- Promo codes table policies
DROP POLICY IF EXISTS promo_codes_select_policy ON promo_codes;
DROP POLICY IF EXISTS promo_codes_insert_policy ON promo_codes;
DROP POLICY IF EXISTS promo_codes_update_policy ON promo_codes;
DROP POLICY IF EXISTS promo_codes_delete_policy ON promo_codes;

-- =============================================
-- USERS TABLE POLICIES
-- =============================================

-- Users SELECT: Users can see their own profile, admins/managers can see all
CREATE POLICY users_select_policy ON users
    FOR SELECT USING (
        auth.uid() = id OR
        is_manager_or_admin()
    );

-- Users INSERT: Only admins can create users
CREATE POLICY users_insert_policy ON users
    FOR INSERT WITH CHECK (
        is_admin()
    );

-- Users UPDATE: Users can update their own profile, admins can update all
CREATE POLICY users_update_policy ON users
    FOR UPDATE USING (
        auth.uid() = id OR
        is_admin()
    )
    WITH CHECK (
        auth.uid() = id OR
        is_admin()
    );

-- Users DELETE: Only admins can delete users
CREATE POLICY users_delete_policy ON users
    FOR DELETE USING (
        is_admin()
    );

-- =============================================
-- ORDERS TABLE POLICIES
-- =============================================

-- Orders SELECT: Complex access control based on user type
CREATE POLICY orders_select_policy ON orders
    FOR SELECT USING (
        -- Customers can see their own orders
        auth.uid() = customer_id OR
        -- Users can see orders they created
        auth.uid() = created_by OR
        -- Admins and managers can see all orders
        is_manager_or_admin() OR
        -- Delivery persons can see their assigned orders
        (
            get_user_role() = 'delivery_person' AND
            auth.uid() = assigned_delivery_person
        )
    );

-- Orders INSERT: Authenticated users can create orders
CREATE POLICY orders_insert_policy ON orders
    FOR INSERT WITH CHECK (
        -- Must be authenticated
        auth.uid() IS NOT NULL AND
        -- Must set created_by to current user
        created_by = auth.uid() AND
        -- Customer must be valid (either self or managed by admin/manager)
        (
            customer_id = auth.uid() OR
            is_manager_or_admin()
        )
    );

-- Orders UPDATE: Restricted based on user type and order ownership
CREATE POLICY orders_update_policy ON orders
    FOR UPDATE USING (
        -- Admins and managers can update all orders
        is_manager_or_admin() OR
        -- Delivery persons can update status of assigned orders
        (
            get_user_role() = 'delivery_person' AND
            auth.uid() = assigned_delivery_person
        ) OR
        -- Customers can update their own orders (limited fields)
        (
            auth.uid() = customer_id AND
            get_user_role() IN ('client', 'reseller')
        )
    )
    WITH CHECK (
        -- Same conditions for WITH CHECK
        is_manager_or_admin() OR
        (
            get_user_role() = 'delivery_person' AND
            auth.uid() = assigned_delivery_person
        ) OR
        (
            auth.uid() = customer_id AND
            get_user_role() IN ('client', 'reseller')
        )
    );

-- Orders DELETE: Only admins can delete orders
CREATE POLICY orders_delete_policy ON orders
    FOR DELETE USING (
        is_admin()
    );

-- =============================================
-- PRODUCTS TABLE POLICIES
-- =============================================

-- Products SELECT: All authenticated users can view products
CREATE POLICY products_select_policy ON products
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

-- Products INSERT: Only admins and managers can create products
CREATE POLICY products_insert_policy ON products
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

-- Products UPDATE: Only admins and managers can update products
CREATE POLICY products_update_policy ON products
    FOR UPDATE USING (
        is_manager_or_admin()
    )
    WITH CHECK (
        is_manager_or_admin()
    );

-- Products DELETE: Only admins can delete products
CREATE POLICY products_delete_policy ON products
    FOR DELETE USING (
        is_admin()
    );

-- =============================================
-- PROMO CODES TABLE POLICIES
-- =============================================

-- Promo codes SELECT: All authenticated users can view active promo codes
CREATE POLICY promo_codes_select_policy ON promo_codes
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND
        (
            -- All users can see active promo codes
            is_active = true OR
            -- Admins and managers can see all promo codes
            is_manager_or_admin()
        )
    );

-- Promo codes INSERT: Only admins and managers can create promo codes
CREATE POLICY promo_codes_insert_policy ON promo_codes
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

-- Promo codes UPDATE: Only admins and managers can update promo codes
CREATE POLICY promo_codes_update_policy ON promo_codes
    FOR UPDATE USING (
        is_manager_or_admin()
    )
    WITH CHECK (
        is_manager_or_admin()
    );

-- Promo codes DELETE: Only admins can delete promo codes
CREATE POLICY promo_codes_delete_policy ON promo_codes
    FOR DELETE USING (
        is_admin()
    );

-- =============================================
-- CUSTOMER PROFILES TABLE POLICIES
-- =============================================

-- Customer profiles SELECT: Users can see their own profile, admins/managers can see all
CREATE POLICY customer_profiles_select_policy ON customer_profiles
    FOR SELECT USING (
        auth.uid() = user_id OR
        can_manage_clients()
    );

-- Customer profiles INSERT: Only admins and managers can create customer profiles
CREATE POLICY customer_profiles_insert_policy ON customer_profiles
    FOR INSERT WITH CHECK (
        can_manage_clients()
    );

-- Customer profiles UPDATE: Users can update their own profile, admins/managers can update all
CREATE POLICY customer_profiles_update_policy ON customer_profiles
    FOR UPDATE USING (
        auth.uid() = user_id OR
        can_manage_clients()
    )
    WITH CHECK (
        auth.uid() = user_id OR
        can_manage_clients()
    );

-- Customer profiles DELETE: Only admins can delete customer profiles
CREATE POLICY customer_profiles_delete_policy ON customer_profiles
    FOR DELETE USING (
        is_admin()
    );

-- =============================================
-- REVIEWS TABLE POLICIES (if exists)
-- =============================================

-- Enable RLS on reviews table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'reviews') THEN
        ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
        
        -- Reviews SELECT: All authenticated users can view approved reviews
        DROP POLICY IF EXISTS reviews_select_policy ON reviews;
        CREATE POLICY reviews_select_policy ON reviews
            FOR SELECT USING (
                auth.uid() IS NOT NULL AND
                (
                    status = 'approved' OR
                    auth.uid() = user_id OR
                    is_manager_or_admin()
                )
            );
        
        -- Reviews INSERT: Authenticated users can create reviews
        DROP POLICY IF EXISTS reviews_insert_policy ON reviews;
        CREATE POLICY reviews_insert_policy ON reviews
            FOR INSERT WITH CHECK (
                auth.uid() IS NOT NULL AND
                auth.uid() = user_id
            );
        
        -- Reviews UPDATE: Users can update their own reviews, admins/managers can update all
        DROP POLICY IF EXISTS reviews_update_policy ON reviews;
        CREATE POLICY reviews_update_policy ON reviews
            FOR UPDATE USING (
                auth.uid() = user_id OR
                is_manager_or_admin()
            )
            WITH CHECK (
                auth.uid() = user_id OR
                is_manager_or_admin()
            );
        
        -- Reviews DELETE: Users can delete their own reviews, admins can delete all
        DROP POLICY IF EXISTS reviews_delete_policy ON reviews;
        CREATE POLICY reviews_delete_policy ON reviews
            FOR DELETE USING (
                auth.uid() = user_id OR
                is_admin()
            );
    END IF;
END $$;

-- =============================================
-- AUDIT LOGS TABLE POLICIES
-- =============================================

-- Enable RLS on audit_logs table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'audit_logs') THEN
        ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
        
        -- Audit logs SELECT: Only admins can view audit logs
        DROP POLICY IF EXISTS audit_logs_select_policy ON audit_logs;
        CREATE POLICY audit_logs_select_policy ON audit_logs
            FOR SELECT USING (
                is_admin()
            );
        
        -- Audit logs INSERT: System can insert audit logs
        DROP POLICY IF EXISTS audit_logs_insert_policy ON audit_logs;
        CREATE POLICY audit_logs_insert_policy ON audit_logs
            FOR INSERT WITH CHECK (
                auth.uid() IS NOT NULL
            );
        
        -- No UPDATE or DELETE policies for audit logs (immutable)
    END IF;
END $$;

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions on security functions
GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_manager_or_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION can_manage_clients() TO authenticated;

-- =============================================
-- VERIFICATION
-- =============================================

-- Create a view to verify RLS policies are working
CREATE OR REPLACE VIEW security_policy_status AS
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd,
    CASE 
        WHEN cmd = 'SELECT' THEN 'Read Access'
        WHEN cmd = 'INSERT' THEN 'Create Access'
        WHEN cmd = 'UPDATE' THEN 'Update Access'
        WHEN cmd = 'DELETE' THEN 'Delete Access'
        ELSE cmd
    END as access_type,
    SUBSTRING(qual, 1, 100) as policy_condition
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, cmd;

-- Success message
SELECT 'Enhanced RLS policies have been successfully applied!' as status;
