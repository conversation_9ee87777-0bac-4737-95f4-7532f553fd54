#!/usr/bin/env node

/**
 * Validation script for Reseller Price Update Fix
 * Tests that reseller price changes are properly saved and synchronized
 */

const { readFileSync } = require('fs');
const { join } = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function validateFileExists(filePath) {
  try {
    readFileSync(filePath, 'utf8');
    return true;
  } catch (error) {
    return false;
  }
}

function validateFileContains(filePath, searchTerms) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const found = [];
    const missing = [];
    
    searchTerms.forEach(term => {
      if (content.includes(term)) {
        found.push(term);
      } else {
        missing.push(term);
      }
    });
    
    return { found, missing };
  } catch (error) {
    return { found: [], missing: searchTerms };
  }
}

async function validateResellerPriceFix() {
  log('🔍 Validating Reseller Price Update Fix...', colors.blue);
  log('');

  let allTestsPassed = true;

  // Test 1: Product Management Component Fix
  log('1. Checking Product Management Component...', colors.yellow);
  const productMgmtPath = join(process.cwd(), 'src/components/inventory/ProductManagement.tsx');
  
  const productMgmtRequiredTerms = [
    'resellerPrice: productData.resellerPrice, // FIXED: Include reseller price',
    'price: productData.price,',
    'const result = await updateProduct(selectedProduct.id, {',
    'const result = await createProduct({'
  ];
  
  const productMgmtValidation = validateFileContains(productMgmtPath, productMgmtRequiredTerms);
  productMgmtValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  productMgmtValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 2: Inventory Service Fix
  log('2. Checking Inventory Service...', colors.yellow);
  const inventoryServicePath = join(process.cwd(), 'src/services/inventoryService.ts');
  
  const inventoryServiceRequiredTerms = [
    'import { realTimeService, syncProductData, syncInventoryData, syncImageData, syncPriceData } from \'./realTimeService\'',
    'reseller_price: productUpdates.resellerPrice,',
    'if (updates.price !== undefined || updates.resellerPrice !== undefined) {',
    'syncPriceData(id, {',
    'newPrice: product.price,',
    'newResellerPrice: product.resellerPrice'
  ];
  
  const inventoryServiceValidation = validateFileContains(inventoryServicePath, inventoryServiceRequiredTerms);
  inventoryServiceValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  inventoryServiceValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 3: Product Form Component
  log('3. Checking Product Form Component...', colors.yellow);
  const productFormPath = join(process.cwd(), 'src/components/inventory/ProductForm.tsx');
  
  const productFormRequiredTerms = [
    'resellerPrice: 0,',
    'resellerPrice: product.resellerPrice,',
    'onChange={(e) => setFormData(prev => ({ ...prev, resellerPrice: parseFloat(e.target.value) }))}'
  ];
  
  const productFormValidation = validateFileContains(productFormPath, productFormRequiredTerms);
  productFormValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  productFormValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 4: Real-time Service Integration
  log('4. Checking Real-time Service Integration...', colors.yellow);
  const realTimeServicePath = join(process.cwd(), 'src/services/realTimeService.ts');
  
  const realTimeServiceRequiredTerms = [
    '\'price-updated\'',
    'export const syncPriceData = (productId: string, priceData: any, userId?: string) => {',
    'realTimeService.emit(\'price-updated\', { productId, ...priceData }, userId);'
  ];
  
  const realTimeServiceValidation = validateFileContains(realTimeServicePath, realTimeServiceRequiredTerms);
  realTimeServiceValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  realTimeServiceValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 5: Data Flow Logic Validation
  log('5. Validating Data Flow Logic...', colors.yellow);
  
  // Test the data flow from form to database
  const mockProductData = {
    title: 'Test Product',
    price: 100,
    resellerPrice: 80,
    stock: 50
  };
  
  // Simulate the update process
  const updatePayload = {
    title: mockProductData.title,
    price: mockProductData.price,
    resellerPrice: mockProductData.resellerPrice,
    stock: mockProductData.stock
  };
  
  // Check that all required fields are present
  const requiredFields = ['title', 'price', 'resellerPrice', 'stock'];
  const missingFields = requiredFields.filter(field => updatePayload[field] === undefined);
  
  if (missingFields.length === 0) {
    log(`   ✅ All required fields present in update payload`, colors.green);
  } else {
    log(`   ❌ Missing fields in update payload: ${missingFields.join(', ')}`, colors.red);
    allTestsPassed = false;
  }

  // Test database mapping
  const dbMapping = {
    title: updatePayload.title,
    price: updatePayload.price,
    reseller_price: updatePayload.resellerPrice, // This is the key mapping
    stock: updatePayload.stock
  };
  
  if (dbMapping.reseller_price === 80) {
    log(`   ✅ Database mapping correct: resellerPrice -> reseller_price`, colors.green);
  } else {
    log(`   ❌ Database mapping error`, colors.red);
    allTestsPassed = false;
  }

  // Test real-time sync data
  const syncData = {
    newPrice: updatePayload.price,
    newResellerPrice: updatePayload.resellerPrice
  };
  
  if (syncData.newPrice === 100 && syncData.newResellerPrice === 80) {
    log(`   ✅ Real-time sync data correct`, colors.green);
  } else {
    log(`   ❌ Real-time sync data error`, colors.red);
    allTestsPassed = false;
  }

  // Final result
  log('');
  if (allTestsPassed) {
    log('🎉 All reseller price update fixes validated successfully!', colors.green);
    log('');
    log('✨ Fixes Applied:', colors.blue);
    log('   • Added resellerPrice field to updateProduct calls', colors.reset);
    log('   • Added resellerPrice field to createProduct calls', colors.reset);
    log('   • Added real-time price synchronization', colors.reset);
    log('   • Proper database field mapping (resellerPrice -> reseller_price)', colors.reset);
    log('   • Cross-user price update notifications', colors.reset);
    log('');
    log('✨ Data Flow:', colors.blue);
    log('   1. User edits reseller price in ProductForm', colors.reset);
    log('   2. ProductManagement passes resellerPrice to updateProduct', colors.reset);
    log('   3. InventoryService maps to reseller_price in database', colors.reset);
    log('   4. Real-time syncPriceData notifies other users', colors.reset);
    log('   5. UI components receive price-updated events', colors.reset);
    log('');
    log('🔧 Components Fixed:', colors.blue);
    log('   • src/components/inventory/ProductManagement.tsx', colors.reset);
    log('   • src/services/inventoryService.ts', colors.reset);
    log('   • Real-time synchronization integration', colors.reset);
  } else {
    log('❌ Some validations failed. Please review the implementation.', colors.red);
    process.exit(1);
  }
}

// Run validation
validateResellerPriceFix().catch(error => {
  log(`Error during validation: ${error.message}`, colors.red);
  process.exit(1);
});
