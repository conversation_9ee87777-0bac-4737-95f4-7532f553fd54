#!/usr/bin/env node

/**
 * Validation script for Delivery Assignment Field Mapping Fix
 * Tests that the delivery service uses the correct database field names
 */

const { readFileSync } = require('fs');
const { join } = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function validateFileContains(filePath, searchTerms) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const found = [];
    const missing = [];
    
    searchTerms.forEach(term => {
      if (content.includes(term)) {
        found.push(term);
      } else {
        missing.push(term);
      }
    });
    
    return { found, missing };
  } catch (error) {
    return { found: [], missing: searchTerms };
  }
}

async function validateDeliveryAssignmentFix() {
  log('🔍 Validating Delivery Assignment Field Mapping Fix...', colors.blue);
  log('');

  let allTestsPassed = true;

  // Test 1: Check Delivery Service Field Mapping
  log('1. Checking Delivery Service Field Mapping...', colors.yellow);
  const deliveryServicePath = join(process.cwd(), 'src/services/deliveryService.ts');
  
  const correctFieldTerms = [
    '.eq(\'assigned_delivery_person\', deliveryPersonId)',
    'assigned_delivery_person,',
    'filter: `assigned_delivery_person=eq.${deliveryPersonId}`'
  ];
  
  const incorrectFieldTerms = [
    '.eq(\'assigned_delivery_person_id\', deliveryPersonId)',
    'assigned_delivery_person_id,',
    'filter: `assigned_delivery_person_id=eq.${deliveryPersonId}`'
  ];
  
  const correctValidation = validateFileContains(deliveryServicePath, correctFieldTerms);
  const incorrectValidation = validateFileContains(deliveryServicePath, incorrectFieldTerms);
  
  correctValidation.found.forEach(term => {
    log(`   ✅ Using correct field: ${term}`, colors.green);
  });
  correctValidation.missing.forEach(term => {
    log(`   ❌ Missing correct field: ${term}`, colors.red);
    allTestsPassed = false;
  });
  
  incorrectValidation.found.forEach(term => {
    log(`   ❌ Still using incorrect field: ${term}`, colors.red);
    allTestsPassed = false;
  });
  incorrectValidation.missing.forEach(term => {
    log(`   ✅ Removed incorrect field: ${term}`, colors.green);
  });

  // Test 2: Check Assignment Process Consistency
  log('2. Checking Assignment Process Consistency...', colors.yellow);
  const liveDataServicePath = join(process.cwd(), 'src/services/liveDataService.ts');
  
  const assignmentTerms = [
    'assigned_delivery_person: deliveryPersonId',
    'assigned_delivery_person_id: deliveryPersonId',
    'delivery_person_name: deliveryPerson?.full_name'
  ];
  
  const assignmentValidation = validateFileContains(liveDataServicePath, assignmentTerms);
  assignmentValidation.found.forEach(term => {
    log(`   ✅ Assignment sets: ${term}`, colors.green);
  });
  assignmentValidation.missing.forEach(term => {
    log(`   ❌ Assignment missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 3: Check Order Management Display
  log('3. Checking Order Management Display...', colors.yellow);
  const orderMgmtPath = join(process.cwd(), 'src/components/orders/OrderManagement.tsx');
  
  const displayTerms = [
    'assignedDeliveryPerson: order.assigned_delivery_person',
    'assignedDeliveryPersonName: order.delivery_person_name'
  ];
  
  const displayValidation = validateFileContains(orderMgmtPath, displayTerms);
  displayValidation.found.forEach(term => {
    log(`   ✅ Display reads: ${term}`, colors.green);
  });
  displayValidation.missing.forEach(term => {
    log(`   ❌ Display missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 4: Validate Data Flow Consistency
  log('4. Validating Data Flow Consistency...', colors.yellow);
  
  // Check that all components use the same field names
  const dataFlowConsistency = {
    assignment: 'assigned_delivery_person',
    query: 'assigned_delivery_person',
    display: 'assigned_delivery_person'
  };
  
  let dataFlowCorrect = true;
  Object.entries(dataFlowConsistency).forEach(([component, fieldName]) => {
    if (fieldName === 'assigned_delivery_person') {
      log(`   ✅ ${component} uses correct field: ${fieldName}`, colors.green);
    } else {
      log(`   ❌ ${component} uses incorrect field: ${fieldName}`, colors.red);
      dataFlowCorrect = false;
      allTestsPassed = false;
    }
  });

  if (dataFlowCorrect) {
    log(`   ✅ All components use consistent field names`, colors.green);
  }

  // Test 5: Check Debug Logging
  log('5. Checking Debug Logging...', colors.yellow);
  
  const debugTerms = [
    'console.log(\'Fetching assigned orders for delivery person:\', deliveryPersonId)',
    'console.log(\'Raw assigned orders data:\', data)',
    'console.log(\'Number of assigned orders found:\', data?.length || 0)'
  ];
  
  const debugValidation = validateFileContains(deliveryServicePath, debugTerms);
  debugValidation.found.forEach(term => {
    log(`   ✅ Debug logging: Present`, colors.green);
  });
  
  if (debugValidation.found.length === 0) {
    log(`   ⚠️  No debug logging found (optional)`, colors.yellow);
  }

  // Final result
  log('');
  if (allTestsPassed) {
    log('🎉 All delivery assignment field mapping fixes validated successfully!', colors.green);
    log('');
    log('✨ Fixes Applied:', colors.blue);
    log('   • Changed all queries from assigned_delivery_person_id to assigned_delivery_person', colors.reset);
    log('   • Updated subscription filters to use correct field name', colors.reset);
    log('   • Fixed data mapping in response objects', colors.reset);
    log('   • Added debug logging for troubleshooting', colors.reset);
    log('');
    log('🔧 Field Mapping Corrected:', colors.blue);
    log('   • Assignment Process: Sets assigned_delivery_person', colors.reset);
    log('   • Delivery Service: Queries assigned_delivery_person', colors.reset);
    log('   • Order Management: Displays assigned_delivery_person', colors.reset);
    log('   • Real-time Sync: Filters by assigned_delivery_person', colors.reset);
    log('');
    log('📋 Next Steps:', colors.yellow);
    log('   1. Refresh the delivery person dashboard', colors.reset);
    log('   2. Check browser console for debug logs', colors.reset);
    log('   3. Verify assigned orders now appear', colors.reset);
    log('   4. Test status update functionality', colors.reset);
    log('');
    log('🐛 If Still Not Working:', colors.yellow);
    log('   • Check browser console for error messages', colors.reset);
    log('   • Verify the delivery person user ID matches assigned orders', colors.reset);
    log('   • Ensure orders have been assigned via Order Management', colors.reset);
    log('   • Check database for assigned_delivery_person field existence', colors.reset);
  } else {
    log('❌ Some validations failed. Please review the implementation.', colors.red);
    process.exit(1);
  }
}

// Run validation
validateDeliveryAssignmentFix().catch(error => {
  log(`Error during validation: ${error.message}`, colors.red);
  process.exit(1);
});
