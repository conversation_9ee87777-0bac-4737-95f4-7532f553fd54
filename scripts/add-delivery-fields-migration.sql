-- =====================================================
-- COMPREHENSIVE DELIVERY SYSTEM DATABASE MIGRATION
-- =====================================================
-- Run this in your Supabase SQL editor to fix delivery assignment

-- Step 1: Add delivery assignment fields to orders table
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS assigned_delivery_person UUID REFERENCES users(id),
ADD COLUMN IF NOT EXISTS assigned_delivery_person_id UUID REFERENCES users(id),
ADD COLUMN IF NOT EXISTS delivery_person_name TEXT,
ADD COLUMN IF NOT EXISTS delivery_assigned_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS delivery_notes TEXT;

-- Step 2: Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_orders_assigned_delivery_person
ON orders(assigned_delivery_person);

CREATE INDEX IF NOT EXISTS idx_orders_assigned_delivery_person_id
ON orders(assigned_delivery_person_id);

CREATE INDEX IF NOT EXISTS idx_orders_delivery_status
ON orders(status) WHERE assigned_delivery_person IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_orders_delivered_at
ON orders(delivered_at) WHERE delivered_at IS NOT NULL;

-- Step 3: Add comments to document the fields
COMMENT ON COLUMN orders.assigned_delivery_person IS 'Primary delivery person ID (main field used by system)';
COMMENT ON COLUMN orders.assigned_delivery_person_id IS 'Alternative delivery person ID (for compatibility)';
COMMENT ON COLUMN orders.delivery_person_name IS 'Name of assigned delivery person (for display)';
COMMENT ON COLUMN orders.delivery_assigned_at IS 'Timestamp when delivery person was assigned';
COMMENT ON COLUMN orders.delivered_at IS 'Timestamp when order was marked as delivered';
COMMENT ON COLUMN orders.delivery_notes IS 'Notes from delivery person';

-- Step 4: Update RLS policies for delivery personnel access
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Delivery personnel can read assigned orders" ON orders;
DROP POLICY IF EXISTS "Delivery personnel can update assigned orders" ON orders;

-- Create new policies for delivery personnel
CREATE POLICY "Delivery personnel can read assigned orders" ON orders
FOR SELECT USING (
  auth.uid() = assigned_delivery_person OR
  auth.uid() = assigned_delivery_person_id
);

CREATE POLICY "Delivery personnel can update assigned orders" ON orders
FOR UPDATE USING (
  auth.uid() = assigned_delivery_person OR
  auth.uid() = assigned_delivery_person_id
) WITH CHECK (
  auth.uid() = assigned_delivery_person OR
  auth.uid() = assigned_delivery_person_id
);

-- Step 5: Ensure both fields are synchronized (trigger function)
CREATE OR REPLACE FUNCTION sync_delivery_person_fields()
RETURNS TRIGGER AS $$
BEGIN
  -- If assigned_delivery_person is set, also set assigned_delivery_person_id
  IF NEW.assigned_delivery_person IS NOT NULL THEN
    NEW.assigned_delivery_person_id := NEW.assigned_delivery_person;
  END IF;

  -- If assigned_delivery_person_id is set, also set assigned_delivery_person
  IF NEW.assigned_delivery_person_id IS NOT NULL THEN
    NEW.assigned_delivery_person := NEW.assigned_delivery_person_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to keep fields synchronized
DROP TRIGGER IF EXISTS sync_delivery_person_fields_trigger ON orders;
CREATE TRIGGER sync_delivery_person_fields_trigger
  BEFORE INSERT OR UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION sync_delivery_person_fields();

-- Step 6: Verify the migration
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'orders'
AND column_name IN (
  'assigned_delivery_person',
  'assigned_delivery_person_id',
  'delivery_person_name',
  'delivery_assigned_at',
  'delivered_at',
  'delivery_notes'
)
ORDER BY column_name;

-- Step 7: Test data integrity
SELECT
  'Migration completed successfully!' as status,
  COUNT(*) as total_orders,
  COUNT(assigned_delivery_person) as assigned_orders
FROM orders;
