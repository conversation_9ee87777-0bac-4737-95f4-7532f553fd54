-- Add 'assigned' status to orders table constraint
-- This fixes the constraint violation by allowing 'assigned' as a valid status

-- Step 1: Check current constraint
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass 
  AND conname LIKE '%status_check%';

-- Step 2: Drop the existing status check constraint
ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_status_check;

-- Step 3: Add new constraint that includes 'assigned' status
ALTER TABLE orders ADD CONSTRAINT orders_status_check 
CHECK (status IN (
  'pending', 
  'confirmed', 
  'preparing', 
  'ready', 
  'assigned',     -- NEW: Added for delivery assignment
  'picked',       -- NEW: Added for delivery workflow  
  'out_for_delivery', -- NEW: Added for delivery workflow
  'shipped', 
  'delivered', 
  'cancelled', 
  'returned'
));

-- Step 4: Verify the new constraint is in place
SELECT 
  conname as constraint_name,
  pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass 
  AND conname LIKE '%status_check%';

-- Step 5: Test that we can now update orders to 'assigned' status
-- (This is just a test - we'll do the actual updates after)
SELECT 
  'Constraint Update Complete' as status,
  'You can now safely update orders to assigned status' as message;

-- Step 6: Show current orders that need to be updated
SELECT 
  'Orders Ready for Status Update' as info_type,
  id,
  order_number,
  status as current_status,
  assigned_delivery_person,
  delivery_person_name
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY created_at DESC;
