#!/usr/bin/env node

/**
 * Test script to check delivery assignment functionality
 * This will help us understand what's happening with the database fields
 */

const { createClient } = require('@supabase/supabase-js');

// You'll need to replace these with your actual Supabase credentials
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'your-supabase-url';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-key';

async function testDeliveryAssignment() {
  console.log('🔍 Testing Delivery Assignment Database Fields...');
  
  if (supabaseUrl === 'your-supabase-url' || supabaseKey === 'your-supabase-key') {
    console.log('❌ Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables');
    console.log('');
    console.log('💡 To test the delivery assignment:');
    console.log('1. Create some test orders in the admin dashboard');
    console.log('2. Assign them to delivery personnel using the truck icon');
    console.log('3. Login as a delivery person to see the assigned orders');
    console.log('');
    console.log('🔧 If you see "No assigned orders", it means:');
    console.log('   • No orders have been assigned to delivery personnel yet');
    console.log('   • The database field mapping needs to be fixed');
    console.log('   • The delivery person user ID doesn\'t match the assigned orders');
    return;
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Check what fields exist in orders table
    console.log('1. Checking orders table structure...');
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('*')
      .limit(1);
    
    if (ordersError) {
      console.log('❌ Error querying orders:', ordersError.message);
      return;
    }
    
    if (orders && orders.length > 0) {
      console.log('✅ Available fields in orders table:');
      Object.keys(orders[0]).forEach(field => {
        if (field.includes('delivery') || field.includes('assigned')) {
          console.log(`   🎯 ${field}: ${orders[0][field]}`);
        } else {
          console.log(`   • ${field}`);
        }
      });
    } else {
      console.log('ℹ️  No orders found in database');
    }
    
    // Test 2: Check for delivery personnel
    console.log('\n2. Checking for delivery personnel...');
    const { data: deliveryPersons, error: deliveryError } = await supabase
      .from('users')
      .select('id, full_name, email, role')
      .eq('role', 'delivery')
      .limit(5);
    
    if (deliveryError) {
      console.log('❌ Error querying delivery personnel:', deliveryError.message);
    } else if (deliveryPersons && deliveryPersons.length > 0) {
      console.log('✅ Found delivery personnel:');
      deliveryPersons.forEach(person => {
        console.log(`   👤 ${person.full_name} (${person.email}) - ID: ${person.id}`);
      });
    } else {
      console.log('ℹ️  No delivery personnel found');
    }
    
    // Test 3: Check for assigned orders
    console.log('\n3. Checking for assigned orders...');
    const { data: assignedOrders, error: assignedError } = await supabase
      .from('orders')
      .select('id, order_number, status, assigned_delivery_person, assigned_delivery_person_id, delivery_person_name')
      .not('assigned_delivery_person_id', 'is', null)
      .limit(5);
    
    if (assignedError) {
      console.log('❌ Error querying assigned orders:', assignedError.message);
    } else if (assignedOrders && assignedOrders.length > 0) {
      console.log('✅ Found assigned orders:');
      assignedOrders.forEach(order => {
        console.log(`   📦 ${order.order_number} - Status: ${order.status}`);
        console.log(`      Assigned to: ${order.delivery_person_name || 'Unknown'} (ID: ${order.assigned_delivery_person_id})`);
      });
    } else {
      console.log('ℹ️  No assigned orders found');
    }
    
  } catch (error) {
    console.log('❌ Error during testing:', error.message);
  }
}

// Instructions for manual testing
console.log('📋 Manual Testing Instructions:');
console.log('');
console.log('1. 🏭 Create Test Orders:');
console.log('   • Login as Admin/Manager');
console.log('   • Go to Order Management');
console.log('   • Create a few test orders');
console.log('');
console.log('2. 🚚 Assign Delivery Personnel:');
console.log('   • In Order Management, click the truck icon next to an order');
console.log('   • Select a delivery person from the modal');
console.log('   • Click "Assign Delivery Person"');
console.log('');
console.log('3. 👤 Test Delivery Dashboard:');
console.log('   • Login as the assigned delivery person');
console.log('   • Go to Delivery Dashboard');
console.log('   • Check if assigned orders appear');
console.log('');
console.log('4. 🔄 Test Status Updates:');
console.log('   • Click "Mark as Picked" on an order');
console.log('   • Then "Out for Delivery"');
console.log('   • Finally "Mark as Delivered"');
console.log('   • Check if changes sync to Admin dashboard');
console.log('');

testDeliveryAssignment();
