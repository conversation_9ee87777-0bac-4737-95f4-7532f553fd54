#!/usr/bin/env node

/**
 * Validation script for YalaOffice Profile Picture and Pricing Fixes
 * Tests both profile picture synchronization and reseller pricing logic
 */

const { readFileSync } = require('fs');
const { join } = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function validateFileExists(filePath) {
  try {
    readFileSync(filePath, 'utf8');
    return true;
  } catch (error) {
    return false;
  }
}

function validateFileContains(filePath, searchTerms) {
  try {
    const content = readFileSync(filePath, 'utf8');
    const found = [];
    const missing = [];
    
    searchTerms.forEach(term => {
      if (content.includes(term)) {
        found.push(term);
      } else {
        missing.push(term);
      }
    });
    
    return { found, missing };
  } catch (error) {
    return { found: [], missing: searchTerms };
  }
}

async function validateFixes() {
  log('🔍 Validating YalaOffice Profile Picture and Pricing Fixes...', colors.blue);
  log('');

  let allTestsPassed = true;

  // Test 1: Profile Picture Service
  log('1. Checking Profile Picture Service...', colors.yellow);
  const profileServicePath = join(process.cwd(), 'src/services/profilePictureService.ts');
  if (validateFileExists(profileServicePath)) {
    log('   ✅ profilePictureService.ts exists', colors.green);
    
    const serviceRequiredTerms = [
      'class ProfilePictureService',
      'initializeRealTimeSubscription',
      'updateProfilePicture',
      'uploadProfilePicture',
      'getDisplayAvatar',
      'generateFallbackAvatar'
    ];
    
    const serviceValidation = validateFileContains(profileServicePath, serviceRequiredTerms);
    serviceValidation.found.forEach(term => {
      log(`   ✅ Found: ${term}`, colors.green);
    });
    serviceValidation.missing.forEach(term => {
      log(`   ❌ Missing: ${term}`, colors.red);
      allTestsPassed = false;
    });
  } else {
    log('   ❌ profilePictureService.ts not found', colors.red);
    allTestsPassed = false;
  }

  // Test 2: Profile Picture Hook
  log('2. Checking Profile Picture Hook...', colors.yellow);
  const hookPath = join(process.cwd(), 'src/hooks/useProfilePicture.ts');
  if (validateFileExists(hookPath)) {
    log('   ✅ useProfilePicture.ts exists', colors.green);
    
    const hookRequiredTerms = [
      'export const useProfilePicture',
      'useMultipleProfilePictures',
      'profilePictureService.subscribe',
      'uploadProfilePicture',
      'updateProfilePicture'
    ];
    
    const hookValidation = validateFileContains(hookPath, hookRequiredTerms);
    hookValidation.found.forEach(term => {
      log(`   ✅ Found: ${term}`, colors.green);
    });
    hookValidation.missing.forEach(term => {
      log(`   ❌ Missing: ${term}`, colors.red);
      allTestsPassed = false;
    });
  } else {
    log('   ❌ useProfilePicture.ts not found', colors.red);
    allTestsPassed = false;
  }

  // Test 3: Unified Avatar Component
  log('3. Checking Unified Avatar Component...', colors.yellow);
  const avatarPath = join(process.cwd(), 'src/components/ui/UnifiedAvatar.tsx');
  if (validateFileExists(avatarPath)) {
    log('   ✅ UnifiedAvatar.tsx exists', colors.green);
    
    const avatarRequiredTerms = [
      'export const UnifiedAvatar',
      'NavigationAvatar',
      'ListAvatar',
      'ProfileAvatar',
      'useProfilePicture'
    ];
    
    const avatarValidation = validateFileContains(avatarPath, avatarRequiredTerms);
    avatarValidation.found.forEach(term => {
      log(`   ✅ Found: ${term}`, colors.green);
    });
    avatarValidation.missing.forEach(term => {
      log(`   ❌ Missing: ${term}`, colors.red);
      allTestsPassed = false;
    });
  } else {
    log('   ❌ UnifiedAvatar.tsx not found', colors.red);
    allTestsPassed = false;
  }

  // Test 4: Cart Pricing Fix
  log('4. Checking Cart Pricing Fix...', colors.yellow);
  const cartPath = join(process.cwd(), 'src/components/cart/Cart.tsx');
  const cartRequiredTerms = [
    'resellerPrice?: number',
    'userType === \'reseller\' ? (item.resellerPrice || item.price) : item.price',
    'text-gray-500 line-through'
  ];
  
  const cartValidation = validateFileContains(cartPath, cartRequiredTerms);
  cartValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  cartValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 5: Admin Order Creation Fix
  log('5. Checking Admin Order Creation Fix...', colors.yellow);
  const orderModalPath = join(process.cwd(), 'src/components/orders/CreateOrderModal.tsx');
  const orderRequiredTerms = [
    'selectedCustomer?.userType === \'reseller\'',
    'product.resellerPrice || product.price',
    'price: customerPrice'
  ];
  
  const orderValidation = validateFileContains(orderModalPath, orderRequiredTerms);
  orderValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  orderValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 6: Navigation Avatar Integration
  log('6. Checking Navigation Avatar Integration...', colors.yellow);
  const dropdownPath = join(process.cwd(), 'src/components/ui/UserProfileDropdown.tsx');
  const dropdownRequiredTerms = [
    'import { NavigationAvatar, UnifiedAvatar }',
    'NavigationAvatar',
    'userId={user?.id || \'\'}'
  ];
  
  const dropdownValidation = validateFileContains(dropdownPath, dropdownRequiredTerms);
  dropdownValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  dropdownValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 7: User Management Avatar Integration
  log('7. Checking User Management Avatar Integration...', colors.yellow);
  const userMgmtPath = join(process.cwd(), 'src/components/admin/UserManagement.tsx');
  const userMgmtRequiredTerms = [
    'import { ListAvatar }',
    'ListAvatar',
    'userId={user.id}'
  ];
  
  const userMgmtValidation = validateFileContains(userMgmtPath, userMgmtRequiredTerms);
  userMgmtValidation.found.forEach(term => {
    log(`   ✅ Found: ${term}`, colors.green);
  });
  userMgmtValidation.missing.forEach(term => {
    log(`   ❌ Missing: ${term}`, colors.red);
    allTestsPassed = false;
  });

  // Test 8: Pricing Logic Validation
  log('8. Validating Pricing Logic...', colors.yellow);
  
  // Test reseller pricing calculation
  const mockProduct = {
    price: 100,
    resellerPrice: 80
  };
  
  const clientPrice = mockProduct.price;
  const resellerPrice = mockProduct.resellerPrice;
  const savings = clientPrice - resellerPrice;
  
  if (resellerPrice < clientPrice) {
    log(`   ✅ Reseller pricing logic correct: ${resellerPrice} < ${clientPrice}`, colors.green);
    log(`   ✅ Savings calculation: ${savings} DH`, colors.green);
  } else {
    log(`   ❌ Pricing logic error`, colors.red);
    allTestsPassed = false;
  }

  // Test cart total calculation for different user types
  const cartItems = [
    { ...mockProduct, quantity: 2 },
    { ...mockProduct, quantity: 1 }
  ];
  
  const clientTotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const resellerTotal = cartItems.reduce((sum, item) => {
    const price = item.resellerPrice || item.price;
    return sum + (price * item.quantity);
  }, 0);
  
  if (clientTotal === 300 && resellerTotal === 240) {
    log(`   ✅ Cart calculations correct: Client=${clientTotal}, Reseller=${resellerTotal}`, colors.green);
  } else {
    log(`   ❌ Cart calculation error: Client=${clientTotal}, Reseller=${resellerTotal}`, colors.red);
    allTestsPassed = false;
  }

  // Final result
  log('');
  if (allTestsPassed) {
    log('🎉 All validations passed! Both fixes are ready.', colors.green);
    log('');
    log('✨ Profile Picture System Features:', colors.blue);
    log('   • Unified profile picture service with real-time sync', colors.reset);
    log('   • Profile pictures in navigation headers across all dashboards', colors.reset);
    log('   • Profile pictures in user management and profile sections', colors.reset);
    log('   • Automatic fallback to initials with YalaOffice colors', colors.reset);
    log('   • Supabase storage integration with database sync', colors.reset);
    log('');
    log('✨ Pricing System Features:', colors.blue);
    log('   • Reseller pricing in cart operations and totals', colors.reset);
    log('   • Admin order creation with user-type-based pricing', colors.reset);
    log('   • Dual pricing display (reseller + crossed-out regular)', colors.reset);
    log('   • Consistent pricing across all product components', colors.reset);
    log('   • Real-time price updates and synchronization', colors.reset);
  } else {
    log('❌ Some validations failed. Please review the implementation.', colors.red);
    process.exit(1);
  }
}

// Run validation
validateFixes().catch(error => {
  log(`Error during validation: ${error.message}`, colors.red);
  process.exit(1);
});
