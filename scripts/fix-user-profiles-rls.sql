-- Quick Fix for User Profiles RLS Issue
-- Run this immediately in Supabase SQL Editor to fix the 406 error

-- =============================================
-- SECURITY FUNCTIONS (if not already exist)
-- =============================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT user_type 
    FROM users 
    WHERE id = auth.uid() 
    AND is_active = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role() = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is manager or admin
CREATE OR REPLACE FUNCTION is_manager_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role() IN ('admin', 'manager');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- FIX USER PROFILES TABLE RLS
-- =============================================

-- Enable RLS on user_profiles table
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing conflicting policies
DROP POLICY IF EXISTS user_profiles_own ON user_profiles;
DROP POLICY IF EXISTS user_profiles_select_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_insert_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_update_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_delete_policy ON user_profiles;

-- Create new secure policies for user_profiles
-- SELECT: Users can see their own profile, admins/managers can see all
CREATE POLICY user_profiles_select_policy ON user_profiles
    FOR SELECT USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

-- INSERT: Users can create their own profile, admins/managers can create any
CREATE POLICY user_profiles_insert_policy ON user_profiles
    FOR INSERT WITH CHECK (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

-- UPDATE: Users can update their own profile, admins/managers can update any
CREATE POLICY user_profiles_update_policy ON user_profiles
    FOR UPDATE USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    )
    WITH CHECK (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

-- DELETE: Only admins can delete user profiles
CREATE POLICY user_profiles_delete_policy ON user_profiles
    FOR DELETE USING (
        is_admin()
    );

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions on security functions
GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_manager_or_admin() TO authenticated;

-- =============================================
-- VERIFICATION
-- =============================================

-- Test the policies are working
SELECT 'User profiles RLS policies have been successfully applied!' as status;

-- Show current policies
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd,
    SUBSTRING(qual, 1, 50) as policy_condition
FROM pg_policies 
WHERE tablename = 'user_profiles'
ORDER BY cmd;
