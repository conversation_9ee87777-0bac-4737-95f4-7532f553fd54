-- Fix Delivery Status Update Error
-- Comprehensive diagnostic and fix for "Failed to update delivery status" error

-- Step 1: Check if delivery status columns exist
SELECT 'STEP 1: Checking Delivery Status Columns' as step;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name LIKE '%delivery%'
ORDER BY column_name;

-- Step 2: Add missing delivery status columns if they don't exist
SELECT 'STEP 2: Adding Missing Delivery Status Columns' as step;

-- Add delivery_status column
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status VARCHAR(20) DEFAULT 'not_assigned' 
CHECK (delivery_status IN ('not_assigned', 'assigned', 'picked', 'out_for_delivery', 'delivered', 'failed', 'returned'));

-- Add audit trail columns
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status_updated_by UUID REFERENCES users(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status_updated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_status_history JSONB DEFAULT '[]'::jsonb;

-- Step 3: Add indexes for performance
SELECT 'STEP 3: Adding Indexes' as step;
CREATE INDEX IF NOT EXISTS idx_orders_delivery_status ON orders(delivery_status);
CREATE INDEX IF NOT EXISTS idx_orders_delivery_status_updated ON orders(delivery_status_updated_at);

-- Step 4: Check RLS policies on orders table
SELECT 'STEP 4: Checking RLS Policies' as step;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd,
    SUBSTRING(qual, 1, 100) as policy_condition
FROM pg_policies 
WHERE tablename = 'orders'
ORDER BY policyname;

-- Step 5: Ensure users can update delivery status
SELECT 'STEP 5: Updating RLS Policies for Delivery Status' as step;

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can update delivery status" ON orders;

-- Create policy for delivery status updates
CREATE POLICY "Users can update delivery status" ON orders
    FOR UPDATE USING (
        -- Admins and managers can update any order
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager', 'store_manager')
        )
        OR
        -- Delivery persons can update their assigned orders
        (
            assigned_delivery_person = auth.uid()
            AND EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND user_type = 'delivery_person'
            )
        )
    )
    WITH CHECK (
        -- Same conditions for WITH CHECK
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager', 'store_manager')
        )
        OR
        (
            assigned_delivery_person = auth.uid()
            AND EXISTS (
                SELECT 1 FROM users 
                WHERE id = auth.uid() 
                AND user_type = 'delivery_person'
            )
        )
    );

-- Step 6: Initialize delivery status for existing orders
SELECT 'STEP 6: Initializing Delivery Status for Existing Orders' as step;
UPDATE orders 
SET 
    delivery_status = CASE 
        WHEN status = 'delivered' THEN 'delivered'
        WHEN status = 'shipped' AND assigned_delivery_person IS NOT NULL THEN 'out_for_delivery'
        WHEN assigned_delivery_person IS NOT NULL THEN 'assigned'
        ELSE 'not_assigned'
    END,
    delivery_status_updated_at = NOW()
WHERE delivery_status IS NULL OR delivery_status = 'not_assigned';

-- Step 7: Test delivery status update functionality
SELECT 'STEP 7: Testing Delivery Status Update' as step;

-- Check current user permissions
SELECT 
    'Current User Check' as test_type,
    auth.uid() as current_user_id,
    u.email,
    u.full_name,
    u.user_type,
    CASE 
        WHEN u.user_type IN ('admin', 'manager', 'store_manager') THEN 'CAN_UPDATE_ANY_ORDER'
        WHEN u.user_type = 'delivery_person' THEN 'CAN_UPDATE_ASSIGNED_ORDERS'
        ELSE 'NO_UPDATE_PERMISSIONS'
    END as delivery_update_permissions
FROM users u
WHERE u.id = auth.uid();

-- Check sample orders that can be updated
SELECT 
    'Sample Orders for Testing' as test_type,
    id,
    order_number,
    status,
    delivery_status,
    assigned_delivery_person,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager', 'store_manager')
        ) THEN 'CAN_UPDATE'
        WHEN assigned_delivery_person = auth.uid() THEN 'CAN_UPDATE_AS_DELIVERY_PERSON'
        ELSE 'CANNOT_UPDATE'
    END as update_permission
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;

-- Step 8: Verification and troubleshooting
SELECT 'STEP 8: Final Verification' as step;

-- Check if all columns exist
SELECT 
    'Column Existence Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'delivery_status'
        ) THEN 'delivery_status: EXISTS'
        ELSE 'delivery_status: MISSING'
    END as status
UNION ALL
SELECT 
    'Column Existence Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'delivery_status_updated_by'
        ) THEN 'delivery_status_updated_by: EXISTS'
        ELSE 'delivery_status_updated_by: MISSING'
    END as status
UNION ALL
SELECT 
    'Column Existence Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'delivery_status_updated_at'
        ) THEN 'delivery_status_updated_at: EXISTS'
        ELSE 'delivery_status_updated_at: MISSING'
    END as status;

-- Show delivery status distribution
SELECT 
    'Delivery Status Distribution' as summary_type,
    delivery_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM orders 
GROUP BY delivery_status
ORDER BY count DESC;

-- Final success message
SELECT 'DELIVERY STATUS UPDATE FIX COMPLETE!' as status;
SELECT 'You can now test delivery status updates in the Order Management page' as next_step;
