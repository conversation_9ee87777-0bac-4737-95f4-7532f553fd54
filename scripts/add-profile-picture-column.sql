-- Add Profile Picture URL Column to Users Table
-- Fixes the missing profile_picture_url column issue

-- Step 1: Add profile_picture_url column if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture_url TEXT;

-- Step 2: Add index for better performance
CREATE INDEX IF NOT EXISTS idx_users_profile_picture_url ON users(profile_picture_url);

-- Step 3: Add comment for documentation
COMMENT ON COLUMN users.profile_picture_url IS 'URL to user profile picture stored in Supabase Storage';

-- Step 4: Update RLS policies to include profile picture access
-- Users can update their own profile picture
DROP POLICY IF EXISTS "Users can update own profile picture" ON users;
CREATE POLICY "Users can update own profile picture" ON users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Step 5: Ensure storage bucket exists and has proper policies
-- Note: This needs to be done in Supabase Storage UI or via API
-- Bucket name: 'avatars'
-- Policies needed:
-- 1. Users can upload their own avatars
-- 2. Public read access for profile pictures

-- Step 6: Verification queries
SELECT 'Profile Picture Column Added Successfully!' as status;

-- Check if column exists
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'profile_picture_url';

-- Show sample users with profile picture info
SELECT 
    id,
    email,
    full_name,
    profile_picture_url,
    CASE 
        WHEN profile_picture_url IS NOT NULL THEN 'HAS_PICTURE'
        ELSE 'NO_PICTURE'
    END as picture_status
FROM users 
ORDER BY created_at DESC 
LIMIT 5;

-- Instructions for Supabase Storage setup
SELECT 'STORAGE SETUP INSTRUCTIONS' as guide;
SELECT 'Create "avatars" bucket in Supabase Storage' as step_1
UNION ALL
SELECT 'Set bucket to public read access' as step_2
UNION ALL
SELECT 'Add policy: Users can upload to own folder' as step_3
UNION ALL
SELECT 'Add policy: Public read access for all files' as step_4;
