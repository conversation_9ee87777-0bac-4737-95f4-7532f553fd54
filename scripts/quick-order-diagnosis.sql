-- Quick Order Management Diagnosis
-- Run this in Supabase SQL Editor to diagnose the "No orders found" issue

-- Step 1: Check if orders table exists and has data
SELECT 'STEP 1: Orders Table Check' as step;
SELECT COUNT(*) as total_orders FROM orders;

-- Step 2: Check if delivery_status column exists
SELECT 'STEP 2: Delivery Status Column Check' as step;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name LIKE '%delivery%'
ORDER BY column_name;

-- Step 3: Check sample order data
SELECT 'STEP 3: Sample Order Data' as step;
SELECT 
    id,
    order_number,
    status,
    delivery_status,
    customer_id,
    total,
    created_at
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;

-- Step 4: Check if users table has admin users
SELECT 'STEP 4: Admin Users Check' as step;
SELECT id, email, full_name, user_type, created_at
FROM users 
WHERE user_type IN ('admin', 'manager', 'store_manager')
ORDER BY created_at DESC;

-- Step 5: Check RLS policies on orders table
SELECT 'STEP 5: RLS Policies Check' as step;
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    cmd,
    SUBSTRING(qual, 1, 100) as policy_condition
FROM pg_policies 
WHERE tablename = 'orders'
ORDER BY policyname;

-- Step 6: Test basic order query that frontend uses
SELECT 'STEP 6: Frontend Query Test' as step;
SELECT 
    o.*,
    u.full_name as customer_name,
    u.email as customer_email,
    u.phone as customer_phone
FROM orders o
LEFT JOIN users u ON o.customer_id = u.id
ORDER BY o.created_at DESC
LIMIT 3;

-- Step 7: Check if order_items exist
SELECT 'STEP 7: Order Items Check' as step;
SELECT COUNT(*) as total_order_items FROM order_items;

-- Step 8: Check for any database errors or constraints
SELECT 'STEP 8: Table Constraints Check' as step;
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass
ORDER BY conname;

-- Step 9: Final diagnosis summary
SELECT 'STEP 9: Diagnosis Summary' as step;
SELECT 
    'Orders Table' as component,
    CASE WHEN EXISTS (SELECT 1 FROM orders LIMIT 1) THEN 'EXISTS' ELSE 'EMPTY/MISSING' END as status
UNION ALL
SELECT 
    'Delivery Status Column' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'orders' AND column_name = 'delivery_status'
    ) THEN 'EXISTS' ELSE 'MISSING' END as status
UNION ALL
SELECT 
    'Admin Users' as component,
    CASE WHEN EXISTS (
        SELECT 1 FROM users WHERE user_type IN ('admin', 'manager')
    ) THEN 'EXISTS' ELSE 'MISSING' END as status
UNION ALL
SELECT 
    'Order Items' as component,
    CASE WHEN EXISTS (SELECT 1 FROM order_items LIMIT 1) THEN 'EXISTS' ELSE 'EMPTY/MISSING' END as status;

-- Instructions for interpreting results
SELECT 'INTERPRETATION GUIDE' as guide;
SELECT 'If total_orders = 0: No orders in database - create test orders' as instruction
UNION ALL
SELECT 'If delivery_status column missing: Run enhanced-delivery-status-schema-corrected.sql' as instruction
UNION ALL
SELECT 'If no admin users: Check user authentication and roles' as instruction
UNION ALL
SELECT 'If RLS policies block access: Review and update policies' as instruction
UNION ALL
SELECT 'If frontend query fails: Check for missing relationships' as instruction;
