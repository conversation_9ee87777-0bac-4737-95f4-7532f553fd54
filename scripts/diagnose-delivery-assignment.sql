-- Comprehensive Delivery Assignment Diagnosis
-- Run this in Supabase SQL Editor to diagnose the assignment issue

-- Step 1: Check if delivery fields exist in orders table
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name IN (
  'assigned_delivery_person', 
  'assigned_delivery_person_id', 
  'delivery_person_name', 
  'delivery_assigned_at',
  'delivered_at'
)
ORDER BY column_name;

-- Step 2: Check all orders with delivery assignments
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  assigned_delivery_person_id,
  delivery_person_name,
  delivery_assigned_at,
  created_at
FROM orders 
WHERE assigned_delivery_person IS NOT NULL 
   OR assigned_delivery_person_id IS NOT NULL
ORDER BY delivery_assigned_at DESC;

-- Step 3: Check specifically for the delivery person "71dc4c47-5aa1-4557-93d7-69de6fcb36f8"
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  assigned_delivery_person_id,
  delivery_person_name,
  delivery_assigned_at,
  created_at,
  total
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
   OR assigned_delivery_person_id = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY delivery_assigned_at DESC;

-- Step 4: Check all orders with status 'assigned' (regardless of delivery person)
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  assigned_delivery_person_id,
  delivery_person_name,
  delivery_assigned_at,
  created_at,
  total
FROM orders 
WHERE status = 'assigned'
ORDER BY created_at DESC;

-- Step 5: Check if the delivery person user exists
SELECT 
  id,
  full_name,
  email,
  role,
  created_at
FROM users 
WHERE id = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8';

-- Step 6: Check all orders with any delivery-related statuses
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  assigned_delivery_person_id,
  delivery_person_name,
  delivery_assigned_at,
  created_at,
  total
FROM orders 
WHERE status IN ('assigned', 'picked', 'out_for_delivery', 'delivered')
ORDER BY created_at DESC
LIMIT 20;

-- Step 7: Count orders by status
SELECT 
  status,
  COUNT(*) as count,
  COUNT(CASE WHEN assigned_delivery_person IS NOT NULL THEN 1 END) as with_delivery_person
FROM orders 
GROUP BY status
ORDER BY count DESC;

-- Step 8: Check recent order updates (last 24 hours)
SELECT 
  id,
  order_number,
  status,
  assigned_delivery_person,
  delivery_person_name,
  updated_at,
  created_at
FROM orders 
WHERE updated_at >= NOW() - INTERVAL '24 hours'
ORDER BY updated_at DESC;

-- Step 9: Diagnostic summary
SELECT 
  'Total Orders' as metric,
  COUNT(*) as value
FROM orders
UNION ALL
SELECT 
  'Orders with Delivery Person Assigned' as metric,
  COUNT(*) as value
FROM orders 
WHERE assigned_delivery_person IS NOT NULL
UNION ALL
SELECT 
  'Orders for Specific Delivery Person' as metric,
  COUNT(*) as value
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
UNION ALL
SELECT 
  'Orders with Status Assigned' as metric,
  COUNT(*) as value
FROM orders 
WHERE status = 'assigned'
UNION ALL
SELECT 
  'Orders with Status Picked' as metric,
  COUNT(*) as value
FROM orders 
WHERE status = 'picked'
UNION ALL
SELECT 
  'Orders with Status Out for Delivery' as metric,
  COUNT(*) as value
FROM orders 
WHERE status = 'out_for_delivery';

-- Step 10: Check if there are any permission issues
-- This will show if the query would work with the current RLS policies
EXPLAIN (ANALYZE, BUFFERS) 
SELECT id, order_number, assigned_delivery_person, status
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
AND status IN ('assigned', 'picked', 'out_for_delivery');
