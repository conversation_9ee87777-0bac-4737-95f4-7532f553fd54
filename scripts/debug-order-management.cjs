#!/usr/bin/env node

/**
 * Debug Order Management Issues
 * Helps identify why Order Management shows "No orders found"
 */

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function debugOrderManagement() {
  log('🔍 ORDER MANAGEMENT DEBUG GUIDE', colors.blue);
  log('=' .repeat(50), colors.blue);
  log('');

  log('❌ ISSUE: Order Management shows "No orders found"', colors.red);
  log('');

  log('🎯 POSSIBLE CAUSES AND SOLUTIONS:', colors.yellow);
  log('-'.repeat(40), colors.yellow);
  log('');

  log('1. 🗄️  DATABASE SCHEMA ISSUE', colors.cyan);
  log('   Problem: New delivery status fields causing query to fail', colors.reset);
  log('   Solution: Check if SQL schema was applied correctly', colors.green);
  log('');
  log('   Debug Steps:', colors.yellow);
  log('   • Open Supabase Dashboard → SQL Editor', colors.reset);
  log('   • Run: SELECT * FROM orders LIMIT 5;', colors.reset);
  log('   • Check if delivery_status column exists', colors.reset);
  log('   • If missing, re-run the enhanced schema script', colors.reset);
  log('');

  log('2. 🔗 FOREIGN KEY RELATIONSHIP ISSUE', colors.cyan);
  log('   Problem: Query trying to join non-existent relationships', colors.reset);
  log('   Solution: Simplified the query to remove complex joins', colors.green);
  log('');
  log('   What was fixed:', colors.yellow);
  log('   • Removed delivery_status_updated_by_user join', colors.reset);
  log('   • Simplified to basic order data fetching', colors.reset);
  log('   • Added null safety for new fields', colors.reset);
  log('');

  log('3. 🔐 PERMISSIONS ISSUE', colors.cyan);
  log('   Problem: Admin user doesn\'t have access to orders table', colors.reset);
  log('   Solution: Check RLS policies and user permissions', colors.green);
  log('');
  log('   Debug Steps:', colors.yellow);
  log('   • Check if you\'re logged in as admin', colors.reset);
  log('   • Verify admin user_type in users table', colors.reset);
  log('   • Check RLS policies on orders table', colors.reset);
  log('');

  log('4. 🌐 BROWSER/CACHE ISSUE', colors.cyan);
  log('   Problem: Old cached data or JavaScript errors', colors.reset);
  log('   Solution: Clear cache and check console', colors.green);
  log('');
  log('   Debug Steps:', colors.yellow);
  log('   • Hard refresh: Ctrl+Shift+R (or Cmd+Shift+R)', colors.reset);
  log('   • Open browser console (F12)', colors.reset);
  log('   • Look for JavaScript errors', colors.reset);
  log('   • Check Network tab for failed requests', colors.reset);
  log('');

  log('🔧 IMMEDIATE TROUBLESHOOTING STEPS:', colors.magenta);
  log('-'.repeat(40), colors.magenta);
  log('');

  log('STEP 1: Verify Database Schema', colors.yellow);
  log('Run in Supabase SQL Editor:', colors.cyan);
  log('');
  log('-- Check if orders table exists and has data', colors.reset);
  log('SELECT COUNT(*) as total_orders FROM orders;', colors.reset);
  log('');
  log('-- Check if delivery_status column exists', colors.reset);
  log('SELECT column_name FROM information_schema.columns', colors.reset);
  log('WHERE table_name = \'orders\' AND column_name LIKE \'%delivery%\';', colors.reset);
  log('');
  log('-- Check sample order data', colors.reset);
  log('SELECT id, order_number, status, delivery_status, created_at', colors.reset);
  log('FROM orders ORDER BY created_at DESC LIMIT 5;', colors.reset);
  log('');

  log('STEP 2: Check Browser Console', colors.yellow);
  log('1. Open Order Management page', colors.reset);
  log('2. Press F12 to open Developer Tools', colors.reset);
  log('3. Go to Console tab', colors.reset);
  log('4. Look for error messages (red text)', colors.reset);
  log('5. Check Network tab for failed API requests', colors.reset);
  log('');

  log('STEP 3: Verify User Permissions', colors.yellow);
  log('Run in Supabase SQL Editor:', colors.cyan);
  log('');
  log('-- Check current user info', colors.reset);
  log('SELECT id, email, full_name, user_type FROM users', colors.reset);
  log('WHERE email = \'<EMAIL>\';', colors.reset);
  log('');
  log('-- Check RLS policies on orders table', colors.reset);
  log('SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual', colors.reset);
  log('FROM pg_policies WHERE tablename = \'orders\';', colors.reset);
  log('');

  log('STEP 4: Test Basic Query', colors.yellow);
  log('Run in Supabase SQL Editor:', colors.cyan);
  log('');
  log('-- Test if you can access orders as admin', colors.reset);
  log('SELECT * FROM orders LIMIT 1;', colors.reset);
  log('');
  log('If this fails, there\'s a permissions issue.', colors.red);
  log('If this works, there\'s a frontend issue.', colors.green);
  log('');

  log('🎯 EXPECTED RESULTS:', colors.green);
  log('-'.repeat(20), colors.green);
  log('');
  log('After fixing the issue:', colors.reset);
  log('✅ Order Management page shows existing orders', colors.green);
  log('✅ New Delivery Status column appears', colors.green);
  log('✅ Status badges are clickable', colors.green);
  log('✅ No JavaScript errors in console', colors.green);
  log('✅ Network requests succeed', colors.green);
  log('');

  log('🚨 COMMON ERROR MESSAGES:', colors.red);
  log('-'.repeat(30), colors.red);
  log('');
  log('Error: "relation does not exist"', colors.red);
  log('Solution: Re-run the database schema script', colors.green);
  log('');
  log('Error: "permission denied for table orders"', colors.red);
  log('Solution: Check RLS policies and user permissions', colors.green);
  log('');
  log('Error: "column delivery_status does not exist"', colors.red);
  log('Solution: Run the enhanced delivery schema script', colors.green);
  log('');
  log('Error: "foreign key constraint does not exist"', colors.red);
  log('Solution: Simplified query should fix this', colors.green);
  log('');

  log('💡 QUICK FIXES TO TRY:', colors.blue);
  log('-'.repeat(25), colors.blue);
  log('');
  log('1. Hard refresh the page (Ctrl+Shift+R)', colors.reset);
  log('2. Clear browser cache and cookies', colors.reset);
  log('3. Try a different browser or incognito mode', colors.reset);
  log('4. Check if other pages work (like Dashboard)', colors.reset);
  log('5. Verify you\'re logged in as admin user', colors.reset);
  log('');

  log('📞 WHAT TO REPORT IF ISSUE PERSISTS:', colors.magenta);
  log('-'.repeat(45), colors.magenta);
  log('');
  log('Include this information:', colors.reset);
  log('• Results of the SQL queries above', colors.reset);
  log('• Browser console error messages', colors.reset);
  log('• Network tab showing failed requests', colors.reset);
  log('• Your admin user email and user_type', colors.reset);
  log('• Whether other pages work correctly', colors.reset);
  log('');

  log('🔄 RECOVERY STEPS:', colors.cyan);
  log('-'.repeat(20), colors.cyan);
  log('');
  log('If all else fails:', colors.reset);
  log('1. Revert to basic order fetching (remove delivery fields)', colors.reset);
  log('2. Test if orders appear without delivery status', colors.reset);
  log('3. Gradually add delivery features back', colors.reset);
  log('4. Identify which specific change caused the issue', colors.reset);
}

// Run the debug guide
debugOrderManagement();

console.log('');
log('🔧 Follow the steps above to identify and fix the Order Management issue.', colors.cyan);
log('🎯 The most likely cause is a database schema or permissions issue.', colors.yellow);
