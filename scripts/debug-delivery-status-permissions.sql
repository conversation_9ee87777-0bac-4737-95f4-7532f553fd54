-- Debug Delivery Status Permissions
-- Run this in Supabase SQL Editor to check user roles and permissions

-- Step 1: Check current user types in the system
SELECT 'STEP 1: User Types in System' as step;
SELECT 
    user_type,
    COUNT(*) as count,
    STRING_AGG(DISTINCT email, ', ') as sample_emails
FROM users 
GROUP BY user_type
ORDER BY count DESC;

-- Step 2: Check admin users specifically
SELECT 'STEP 2: Admin Users' as step;
SELECT 
    id,
    email,
    full_name,
    user_type,
    created_at
FROM users 
WHERE user_type IN ('admin', 'manager', 'store_manager')
ORDER BY created_at DESC;

-- Step 3: Check if delivery_status column exists in orders
SELECT 'STEP 3: Delivery Status Column Check' as step;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name LIKE '%delivery%'
ORDER BY column_name;

-- Step 4: Check sample orders with delivery status
SELECT 'STEP 4: Sample Orders with Delivery Status' as step;
SELECT 
    id,
    order_number,
    status,
    delivery_status,
    assigned_delivery_person,
    delivery_person_name,
    created_at
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;

-- Step 5: Check RLS policies on orders table
SELECT 'STEP 5: RLS Policies on Orders Table' as step;
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd,
    SUBSTRING(qual, 1, 200) as policy_condition
FROM pg_policies 
WHERE tablename = 'orders'
ORDER BY policyname;

-- Step 6: Test current user permissions
SELECT 'STEP 6: Current User Check' as step;
SELECT 
    auth.uid() as current_user_id,
    u.email,
    u.full_name,
    u.user_type,
    CASE 
        WHEN u.user_type IN ('admin', 'manager', 'store_manager') THEN 'CAN_EDIT_DELIVERY_STATUS'
        WHEN u.user_type = 'delivery_person' THEN 'CAN_UPDATE_OWN_DELIVERIES'
        ELSE 'READ_ONLY'
    END as delivery_permissions
FROM users u
WHERE u.id = auth.uid();

-- Step 7: Check if there are any constraint issues
SELECT 'STEP 7: Delivery Status Constraints' as step;
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass
AND conname LIKE '%delivery%'
ORDER BY conname;

-- Step 8: Diagnosis Summary
SELECT 'STEP 8: Permission Diagnosis' as step;
SELECT 
    'User Authentication' as check_type,
    CASE 
        WHEN auth.uid() IS NOT NULL THEN 'AUTHENTICATED'
        ELSE 'NOT_AUTHENTICATED'
    END as status
UNION ALL
SELECT 
    'User Type Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type IN ('admin', 'manager', 'store_manager')
        ) THEN 'HAS_ADMIN_PERMISSIONS'
        WHEN EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND user_type = 'delivery_person'
        ) THEN 'HAS_DELIVERY_PERMISSIONS'
        ELSE 'NO_DELIVERY_PERMISSIONS'
    END as status
UNION ALL
SELECT 
    'Delivery Status Column' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'orders' AND column_name = 'delivery_status'
        ) THEN 'EXISTS'
        ELSE 'MISSING'
    END as status;

-- Instructions for fixing common issues
SELECT 'TROUBLESHOOTING GUIDE' as guide;
SELECT 'If user_type is not admin/manager/store_manager: Update user_type in users table' as instruction
UNION ALL
SELECT 'If delivery_status column missing: Run enhanced-delivery-status-schema-corrected.sql' as instruction
UNION ALL
SELECT 'If RLS policies blocking: Check and update policies for your user_type' as instruction
UNION ALL
SELECT 'If user not authenticated: Ensure proper login and session' as instruction;
