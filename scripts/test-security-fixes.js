#!/usr/bin/env node

/**
 * Security Fixes Verification Script
 * Tests that all critical security vulnerabilities have been fixed
 */

import fs from 'fs';
import path from 'path';

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(level, message) {
  const timestamp = new Date().toISOString();
  const color = colors[level] || colors.reset;
  console.log(`${color}[${level.toUpperCase()}]${colors.reset} ${timestamp} - ${message}`);
}

function success(message) { log('green', `✅ ${message}`); }
function error(message) { log('red', `❌ ${message}`); }
function warning(message) { log('yellow', `⚠️  ${message}`); }
function info(message) { log('blue', `ℹ️  ${message}`); }

// Test 1: Check that hardcoded secrets are removed
function testHardcodedSecrets() {
  info('Testing for hardcoded secrets...');
  
  const supabaseClientPath = 'src/integrations/supabase/client.ts';
  
  if (!fs.existsSync(supabaseClientPath)) {
    error('Supabase client file not found');
    return false;
  }
  
  const content = fs.readFileSync(supabaseClientPath, 'utf8');
  
  // Check for hardcoded URLs or keys
  const hardcodedPatterns = [
    /const SUPABASE_URL = "https:\/\/.*\.supabase\.co"/,
    /const SUPABASE_PUBLISHABLE_KEY = "eyJ.*"/,
    /https:\/\/umzikqwughlzkiarldoa\.supabase\.co/,
    /eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\./
  ];
  
  let hasHardcodedSecrets = false;
  hardcodedPatterns.forEach(pattern => {
    if (pattern.test(content)) {
      hasHardcodedSecrets = true;
    }
  });
  
  if (hasHardcodedSecrets) {
    error('Hardcoded secrets still found in Supabase client');
    return false;
  }
  
  // Check for environment variable usage
  if (!content.includes('import.meta.env.VITE_SUPABASE_URL') && 
      !content.includes('env.supabase.url')) {
    error('Environment variables not being used for Supabase configuration');
    return false;
  }
  
  success('No hardcoded secrets found - using environment variables');
  return true;
}

// Test 2: Check SQL injection protection
function testSQLInjectionProtection() {
  info('Testing SQL injection protection...');
  
  const promoCodesServicePath = 'src/services/promoCodesService.ts';
  
  if (!fs.existsSync(promoCodesServicePath)) {
    warning('PromoCodesService not found - skipping SQL injection test');
    return true;
  }
  
  const content = fs.readFileSync(promoCodesServicePath, 'utf8');
  
  // Check for vulnerable patterns
  const vulnerablePatterns = [
    /query\.or\(`.*\$\{.*\}.*`\)/,
    /\.ilike\.%\$\{.*\}%/,
    /query = query\.or\(`code\.ilike\.%\$\{filters\.search\}%/
  ];
  
  let hasVulnerablePatterns = false;
  vulnerablePatterns.forEach(pattern => {
    if (pattern.test(content)) {
      hasVulnerablePatterns = true;
    }
  });
  
  if (hasVulnerablePatterns) {
    error('SQL injection vulnerabilities still present');
    return false;
  }
  
  // Check for secure query builder usage
  if (content.includes('SecureQueryBuilder.buildSearchQuery')) {
    success('Secure query builder is being used');
    return true;
  } else {
    warning('Secure query builder usage not detected - manual verification needed');
    return true;
  }
}

// Test 3: Check server-side authorization
function testServerSideAuthorization() {
  info('Testing server-side authorization...');
  
  const authContextPath = 'src/contexts/AuthContext.tsx';
  const authServicePath = 'src/services/authorizationService.ts';
  
  if (!fs.existsSync(authContextPath)) {
    error('AuthContext not found');
    return false;
  }
  
  if (!fs.existsSync(authServicePath)) {
    error('AuthorizationService not found');
    return false;
  }
  
  const authContextContent = fs.readFileSync(authContextPath, 'utf8');
  const authServiceContent = fs.readFileSync(authServicePath, 'utf8');
  
  // Check AuthContext for async permission validation
  if (!authContextContent.includes('hasPermission: (permission: string) => Promise<boolean>')) {
    error('AuthContext does not have async permission validation');
    return false;
  }
  
  // Check for AuthorizationService import
  if (!authContextContent.includes('AuthorizationService')) {
    error('AuthContext does not use AuthorizationService');
    return false;
  }
  
  // Check AuthorizationService for server-side validation
  if (!authServiceContent.includes('validateSession') || 
      !authServiceContent.includes('hasPermission')) {
    error('AuthorizationService missing server-side validation methods');
    return false;
  }
  
  success('Server-side authorization is implemented');
  return true;
}

// Test 4: Check environment configuration
function testEnvironmentConfiguration() {
  info('Testing environment configuration...');
  
  const envConfigPath = 'src/config/environment.ts';
  const envExamplePath = '.env.example';
  const envPath = '.env';
  
  if (!fs.existsSync(envConfigPath)) {
    error('Environment configuration file not found');
    return false;
  }
  
  if (!fs.existsSync(envExamplePath)) {
    error('Environment example file not found');
    return false;
  }
  
  if (!fs.existsSync(envPath)) {
    error('.env file not found');
    return false;
  }
  
  const envConfigContent = fs.readFileSync(envConfigPath, 'utf8');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  // Check environment config has validation
  if (!envConfigContent.includes('validateEnvironment')) {
    error('Environment validation not implemented');
    return false;
  }
  
  // Check .env has required variables
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_SESSION_TIMEOUT',
    'VITE_MAX_LOGIN_ATTEMPTS'
  ];
  
  let missingVars = [];
  requiredVars.forEach(varName => {
    if (!envContent.includes(varName)) {
      missingVars.push(varName);
    }
  });
  
  if (missingVars.length > 0) {
    error(`Missing environment variables: ${missingVars.join(', ')}`);
    return false;
  }
  
  success('Environment configuration is properly set up');
  return true;
}

// Test 5: Check security middleware
function testSecurityMiddleware() {
  info('Testing security middleware...');
  
  const middlewarePath = 'src/middleware/securityMiddleware.ts';
  const secureQueryPath = 'src/services/secureQueryService.ts';
  
  if (!fs.existsSync(middlewarePath)) {
    error('Security middleware not found');
    return false;
  }
  
  if (!fs.existsSync(secureQueryPath)) {
    error('Secure query service not found');
    return false;
  }
  
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  const secureQueryContent = fs.readFileSync(secureQueryPath, 'utf8');
  
  // Check middleware has security validation
  if (!middlewareContent.includes('SecurityMiddleware') || 
      !middlewareContent.includes('performSecurityCheck')) {
    error('Security middleware missing required methods');
    return false;
  }
  
  // Check secure query service has input sanitization
  if (!secureQueryContent.includes('InputSanitizer') || 
      !secureQueryContent.includes('SecureQueryBuilder')) {
    error('Secure query service missing required classes');
    return false;
  }
  
  success('Security middleware and secure query service are implemented');
  return true;
}

// Test 6: Check RLS policies file
function testRLSPolicies() {
  info('Testing RLS policies...');
  
  const rlsPoliciesPath = 'scripts/enhanced-rls-policies.sql';
  
  if (!fs.existsSync(rlsPoliciesPath)) {
    error('Enhanced RLS policies file not found');
    return false;
  }
  
  const content = fs.readFileSync(rlsPoliciesPath, 'utf8');
  
  // Check for essential policies
  const requiredPolicies = [
    'users_select_policy',
    'orders_select_policy',
    'products_select_policy',
    'get_user_role()',
    'is_admin()',
    'is_manager_or_admin()'
  ];
  
  let missingPolicies = [];
  requiredPolicies.forEach(policy => {
    if (!content.includes(policy)) {
      missingPolicies.push(policy);
    }
  });
  
  if (missingPolicies.length > 0) {
    error(`Missing RLS policies: ${missingPolicies.join(', ')}`);
    return false;
  }
  
  success('Enhanced RLS policies are available for deployment');
  return true;
}

// Main test runner
function runSecurityTests() {
  console.log('\n🔐 YalaOffice Security Fixes Verification');
  console.log('==========================================\n');
  
  const tests = [
    { name: 'Hardcoded Secrets Removal', test: testHardcodedSecrets },
    { name: 'SQL Injection Protection', test: testSQLInjectionProtection },
    { name: 'Server-Side Authorization', test: testServerSideAuthorization },
    { name: 'Environment Configuration', test: testEnvironmentConfiguration },
    { name: 'Security Middleware', test: testSecurityMiddleware },
    { name: 'RLS Policies', test: testRLSPolicies }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  tests.forEach((testCase, index) => {
    console.log(`\n${index + 1}. ${testCase.name}`);
    console.log('-'.repeat(40));
    
    try {
      if (testCase.test()) {
        passedTests++;
      }
    } catch (err) {
      error(`Test failed with error: ${err.message}`);
    }
  });
  
  console.log('\n==========================================');
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    success('🎉 All security tests passed! Your system is secure.');
    console.log('\n✅ Critical vulnerabilities have been successfully fixed:');
    console.log('   • Exposed Supabase keys → Environment variables');
    console.log('   • SQL injection → Parameterized queries');
    console.log('   • Client-side permissions → Server-side validation');
    console.log('\n🚀 Next steps:');
    console.log('   1. Deploy RLS policies to Supabase');
    console.log('   2. Test the application thoroughly');
    console.log('   3. Monitor security logs');
  } else {
    error(`❌ ${totalTests - passedTests} security tests failed. Please review and fix the issues.`);
    process.exit(1);
  }
}

// Run the tests
runSecurityTests();
