-- Check Database Constraints and Valid Status Values
-- Run this to understand what status values are allowed

-- Step 1: Check the orders table constraint definition
SELECT 
  conname as constraint_name,
  consrc as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'orders'::regclass 
  AND conname LIKE '%status%';

-- Step 2: Check what status values currently exist in the database
SELECT 
  'Current Status Values in Database' as info_type,
  status,
  COUNT(*) as count
FROM orders 
GROUP BY status 
ORDER BY count DESC;

-- Step 3: Check the table definition for status column
SELECT 
  column_name,
  data_type,
  column_default,
  is_nullable,
  character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'orders' 
  AND column_name LIKE '%status%';

-- Step 4: Check specific orders assigned to delivery person
SELECT 
  'Orders for Delivery Person' as info_type,
  id,
  order_number,
  status,
  payment_status,
  assigned_delivery_person,
  delivery_person_name,
  created_at
FROM orders 
WHERE assigned_delivery_person = '71dc4c47-5aa1-4557-93d7-69de6fcb36f8'
ORDER BY created_at DESC;

-- Step 5: Try to understand what status transitions are allowed
-- Check if there are any triggers or additional constraints
SELECT 
  trigger_name,
  event_manipulation,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'orders';

-- Step 6: Check the exact constraint that's failing
SELECT 
  constraint_name,
  constraint_type,
  check_clause
FROM information_schema.check_constraints 
WHERE constraint_name LIKE '%orders%status%';

-- Step 7: Test what happens if we try different status values
-- (This is just for analysis - don't actually run the updates)
SELECT 
  'Testing Status Values' as test_type,
  'These are the status values we could try:' as message,
  unnest(ARRAY['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']) as possible_status;

-- Step 8: Check if the issue is with the delivery fields themselves
SELECT 
  'Delivery Field Analysis' as analysis_type,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'orders' 
  AND column_name IN ('assigned_delivery_person', 'delivery_person_name', 'delivery_assigned_at');
