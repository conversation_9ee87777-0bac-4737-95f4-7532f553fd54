-- EMERGENCY DATABASE FIX for YalaOffice
-- This script fixes all critical database issues causing 400/406 errors
-- Run this IMMEDIATELY in Supabase SQL Editor

-- =============================================
-- SECURITY FUNCTIONS
-- =============================================

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT user_type 
    FROM users 
    WHERE id = auth.uid() 
    AND is_active = true
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN COALESCE(get_user_role() = 'admin', false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is manager or admin
CREATE OR REPLACE FUNCTION is_manager_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN COALESCE(get_user_role() IN ('admin', 'manager'), false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- MISSING DATABASE FUNCTIONS
-- =============================================

-- Drop any existing versions of the function to avoid conflicts
DROP FUNCTION IF EXISTS get_orders_for_user();
DROP FUNCTION IF EXISTS get_orders_for_user(UUID);
DROP FUNCTION IF EXISTS get_orders_for_user(user_id_param UUID);

-- Function to get orders for user (fixing the 400 error)
CREATE OR REPLACE FUNCTION get_orders_for_user(user_id_param UUID DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    customer_id UUID,
    customer_name TEXT,
    total_amount DECIMAL,
    status TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Use current user if no user_id provided
    IF user_id_param IS NULL THEN
        user_id_param := auth.uid();
    END IF;
    
    -- Return orders based on user role
    RETURN QUERY
    SELECT
        o.id,
        CAST(o.order_number AS TEXT),
        o.customer_id,
        CAST(u.full_name AS TEXT) as customer_name,
        o.total_amount,
        CAST(o.status AS TEXT),
        o.created_at,
        o.updated_at
    FROM orders o
    LEFT JOIN users u ON o.customer_id = u.id
    WHERE 
        -- Admin/Manager can see all orders
        (get_user_role() IN ('admin', 'manager')) OR
        -- Customers can see their own orders
        (o.customer_id = user_id_param) OR
        -- Delivery persons can see assigned orders
        (get_user_role() = 'delivery_person' AND o.assigned_delivery_person = user_id_param)
    ORDER BY o.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simpler version without parameters for frontend compatibility
CREATE OR REPLACE FUNCTION get_orders_for_user()
RETURNS TABLE (
    id UUID,
    order_number TEXT,
    customer_id UUID,
    customer_name TEXT,
    total_amount DECIMAL,
    status TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Call the main function with current user
    RETURN QUERY
    SELECT * FROM get_orders_for_user(auth.uid());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- FIX RLS POLICIES
-- =============================================

-- Enable RLS on all necessary tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;

-- Drop all existing conflicting policies
DROP POLICY IF EXISTS users_own_profile ON users;
DROP POLICY IF EXISTS users_access_policy ON users;
DROP POLICY IF EXISTS users_select_policy ON users;
DROP POLICY IF EXISTS users_insert_policy ON users;
DROP POLICY IF EXISTS users_update_policy ON users;
DROP POLICY IF EXISTS users_delete_policy ON users;

DROP POLICY IF EXISTS user_profiles_own ON user_profiles;
DROP POLICY IF EXISTS user_profiles_select_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_insert_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_update_policy ON user_profiles;
DROP POLICY IF EXISTS user_profiles_delete_policy ON user_profiles;

DROP POLICY IF EXISTS orders_customer_own ON orders;
DROP POLICY IF EXISTS orders_select_policy ON orders;
DROP POLICY IF EXISTS orders_insert_policy ON orders;
DROP POLICY IF EXISTS orders_update_policy ON orders;
DROP POLICY IF EXISTS orders_delete_policy ON orders;

DROP POLICY IF EXISTS products_select_policy ON products;
DROP POLICY IF EXISTS products_insert_policy ON products;
DROP POLICY IF EXISTS products_update_policy ON products;
DROP POLICY IF EXISTS products_delete_policy ON products;

DROP POLICY IF EXISTS customer_profiles_own ON customer_profiles;
DROP POLICY IF EXISTS customer_profiles_select_policy ON customer_profiles;
DROP POLICY IF EXISTS customer_profiles_insert_policy ON customer_profiles;
DROP POLICY IF EXISTS customer_profiles_update_policy ON customer_profiles;
DROP POLICY IF EXISTS customer_profiles_delete_policy ON customer_profiles;

-- =============================================
-- USERS TABLE POLICIES
-- =============================================

CREATE POLICY users_select_policy ON users
    FOR SELECT USING (
        auth.uid() = id OR
        is_manager_or_admin()
    );

CREATE POLICY users_insert_policy ON users
    FOR INSERT WITH CHECK (
        is_admin()
    );

CREATE POLICY users_update_policy ON users
    FOR UPDATE USING (
        auth.uid() = id OR
        is_admin()
    )
    WITH CHECK (
        auth.uid() = id OR
        is_admin()
    );

-- =============================================
-- USER PROFILES TABLE POLICIES
-- =============================================

CREATE POLICY user_profiles_select_policy ON user_profiles
    FOR SELECT USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY user_profiles_insert_policy ON user_profiles
    FOR INSERT WITH CHECK (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY user_profiles_update_policy ON user_profiles
    FOR UPDATE USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    )
    WITH CHECK (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

-- =============================================
-- ORDERS TABLE POLICIES
-- =============================================

CREATE POLICY orders_select_policy ON orders
    FOR SELECT USING (
        auth.uid() = customer_id OR
        auth.uid() = created_by OR
        is_manager_or_admin() OR
        (get_user_role() = 'delivery_person' AND auth.uid() = assigned_delivery_person)
    );

CREATE POLICY orders_insert_policy ON orders
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND
        (
            customer_id = auth.uid() OR
            is_manager_or_admin()
        )
    );

CREATE POLICY orders_update_policy ON orders
    FOR UPDATE USING (
        is_manager_or_admin() OR
        (get_user_role() = 'delivery_person' AND auth.uid() = assigned_delivery_person) OR
        (auth.uid() = customer_id AND get_user_role() IN ('client', 'reseller'))
    );

-- =============================================
-- PRODUCTS TABLE POLICIES
-- =============================================

CREATE POLICY products_select_policy ON products
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

CREATE POLICY products_insert_policy ON products
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY products_update_policy ON products
    FOR UPDATE USING (
        is_manager_or_admin()
    );

-- =============================================
-- CUSTOMER PROFILES TABLE POLICIES
-- =============================================

CREATE POLICY customer_profiles_select_policy ON customer_profiles
    FOR SELECT USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

CREATE POLICY customer_profiles_insert_policy ON customer_profiles
    FOR INSERT WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY customer_profiles_update_policy ON customer_profiles
    FOR UPDATE USING (
        auth.uid() = user_id OR
        is_manager_or_admin()
    );

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

GRANT EXECUTE ON FUNCTION get_user_role() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION is_manager_or_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_orders_for_user(UUID) TO authenticated;

-- =============================================
-- CREATE MISSING INDEXES
-- =============================================

CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

-- =============================================
-- SUCCESS MESSAGE
-- =============================================

SELECT 'Emergency database fix completed successfully! All errors should be resolved.' as status;
