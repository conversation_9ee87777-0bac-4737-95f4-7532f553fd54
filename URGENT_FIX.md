# 🚨 URGENT: Fix User Profiles 406 Error

## **Problem Identified**
The application is getting a 406 HTTP error when accessing `user_profiles` table because the RLS (Row Level Security) policies are not properly configured.

## **Immediate Solution (5 minutes)**

### **Step 1: Open Supabase SQL Editor**
1. Go to: https://supabase.com/dashboard/project/umzikqwughlzkiarldoa/sql
2. Login to your Supabase account

### **Step 2: Run the Quick Fix**
1. Open the file: `scripts/fix-user-profiles-rls.sql`
2. Copy ALL the content from that file
3. Paste it into the Supabase SQL Editor
4. Click "Run" to execute

### **Step 3: Verify the Fix**
You should see:
- "User profiles RLS policies have been successfully applied!"
- A list of the new policies created

### **Step 4: Test the Application**
1. Refresh your browser at http://localhost:8081/
2. The 406 error should be resolved
3. User profiles should load correctly

## **What This Fix Does**

### **Security Functions Created:**
- `get_user_role()` - Gets current user's role from database
- `is_admin()` - Checks if user is admin
- `is_manager_or_admin()` - Checks if user is admin or manager

### **RLS Policies Applied:**
- **SELECT:** Users can see their own profile, admins/managers see all
- **INSERT:** Users can create their own profile, admins/managers can create any
- **UPDATE:** Users can update their own profile, admins/managers can update any
- **DELETE:** Only admins can delete profiles

## **Why This Happened**
The original database schema had basic RLS policies, but they weren't comprehensive enough for the security requirements. The enhanced security system needs more granular permission controls.

## **After the Fix**
Once you run this SQL script:
- ✅ The 406 error will be resolved
- ✅ User profiles will load correctly
- ✅ Proper security will be maintained
- ✅ Different user types will have appropriate access

## **Next Steps (Optional)**
After fixing the immediate issue, you can run the complete enhanced RLS policies:
1. Open `scripts/enhanced-rls-policies.sql`
2. Copy and run it in Supabase SQL Editor
3. This will apply comprehensive security to all tables

## **Verification Commands**
After running the fix, you can verify it worked by running this in SQL Editor:
```sql
-- Check if policies exist
SELECT policyname, cmd FROM pg_policies WHERE tablename = 'user_profiles';

-- Test access (should not return error)
SELECT COUNT(*) FROM user_profiles;
```

**This fix will resolve the 406 error immediately and restore full functionality to your application!** 🚀
