// Secure Supabase client configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { env } from '../../config/environment';

// Get validated Supabase configuration from secure environment config
const SUPABASE_URL = env.supabase.url;
const SUPABASE_PUBLISHABLE_KEY = env.supabase.anonKey;

// Create Supabase client with security configurations
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    // Enhanced security settings
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Prevent session storage in localStorage for enhanced security
    storage: window.sessionStorage,
    // Add security headers
    flowType: 'pkce'
  },
  db: {
    // Add connection security
    schema: 'public'
  },
  global: {
    // Add request headers for security
    headers: {
      'X-Client-Info': 'yalaoffice-web'
    }
  }
});