
import { useState, useEffect } from 'react';
import { LogOut, Bell, Menu, User } from 'lucide-react';
import AdminDashboard from './dashboards/AdminDashboard';
import StoreManagerDashboard from './dashboards/StoreManagerDashboard';
import DeliveryDashboard from './dashboards/DeliveryDashboard';
import ClientDashboard from './dashboards/ClientDashboard';
import ResellerDashboard from './dashboards/ResellerDashboard';
import NotificationSystem from './notifications/NotificationSystem';
import NotificationBell from './ui/NotificationBell';
import ResponsiveLayout from './layout/ResponsiveLayout';
import ResponsiveNavigation from './navigation/ResponsiveNavigation';
import { useMobileUtils } from '../hooks/use-mobile';

// Import management components
import UserManagement from './admin/UserManagement';
import ProductManagement from './inventory/ProductManagement';
import OrderManagement from './orders/OrderManagement';
import AdvancedAnalyticsDashboard from './analytics/AdvancedAnalyticsDashboard';
import SystemAdministration from './system/SystemAdministration';
import ProfileManagement from './profile/ProfileManagement';

interface DashboardProps {
  user: any;
  onLogout: () => void;
  onNavigateToProfile?: () => void;
  dashboardNavigateRef?: React.MutableRefObject<((page: string) => void) | null>;
}

const Dashboard = ({ user, onLogout, onNavigateToProfile, dashboardNavigateRef }: DashboardProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [activeTab, setActiveTab] = useState('overview');
  const [dashboardNavigate, setDashboardNavigate] = useState<((page: string) => void) | null>(null);

  // Enhanced responsive utilities
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();

  // Handle navigation between pages and tabs
  const handleNavigate = (page: string, tab?: string) => {
    console.log('Dashboard: Navigation to', { page, tab });
    setCurrentPage(page);

    if (page === 'dashboard' && tab) {
      setActiveTab(tab);
    }

    // For client/reseller users, pass navigation to appropriate dashboard
    if ((user.userType === 'client' || user.userType === 'reseller') && dashboardNavigate) {
      dashboardNavigate(page);
    }

    // Update the navigation function for child components
    const navFunction = (childPage: string) => handleNavigate(childPage);
    setDashboardNavigate(() => navFunction);
  };

  // Handle profile navigation
  const handleNavigateToProfile = () => {
    console.log('Dashboard: Navigating to profile');
    setCurrentPage('profile');
    setActiveTab('');
  };

  // Update the ref so Index.tsx can access the navigation function
  useEffect(() => {
    if (dashboardNavigateRef) {
      dashboardNavigateRef.current = (page: string) => handleNavigate(page);
    }
  }, [dashboardNavigateRef]);



  const renderPageContent = () => {
    const commonProps = {
      user,
      onLogout,
      onNavigateToProfile,
      dashboardNavigateRef: { current: (page: string) => handleNavigate(page) }
    };

    // Special props for ClientDashboard to handle bottom navigation
    const clientProps = {
      ...commonProps,
      dashboardNavigateRef: { current: dashboardNavigate }
    };

    // Route to different pages based on currentPage
    switch (currentPage) {
      case 'dashboard':
        // Render the appropriate dashboard based on user type
        switch (user.userType) {
          case 'admin':
            return <AdminDashboard {...commonProps} />;
          case 'manager':
            return <StoreManagerDashboard {...commonProps} />;
          case 'delivery':
          case 'delivery_person':
            return <DeliveryDashboard {...commonProps} />;
          case 'client':
            return <ClientDashboard {...clientProps} />;
          case 'reseller':
            return <ResellerDashboard {...clientProps} />;
          default:
            return <ClientDashboard {...commonProps} />;
        }

      case 'user-management':
        return <UserManagement currentUserId={user.id} />;

      case 'product-management':
        return <ProductManagement />;

      case 'order-management':
        return <OrderManagement />;

      case 'analytics':
        return <AdvancedAnalyticsDashboard />;

      case 'system-settings':
        return <SystemAdministration />;

      case 'profile':
        return <ProfileManagement user={user} />;

      default:
        // Default to dashboard for the user's type
        switch (user.userType) {
          case 'admin':
            return <AdminDashboard {...commonProps} />;
          case 'manager':
            return <StoreManagerDashboard {...commonProps} />;
          case 'delivery':
          case 'delivery_person':
            return <DeliveryDashboard {...commonProps} />;
          case 'client':
            return <ClientDashboard {...clientProps} />;
          case 'reseller':
            return <ResellerDashboard {...clientProps} />;
          default:
            return <ClientDashboard {...commonProps} />;
        }
    }
  };

  // Close dropdowns when clicking outside
  const handleOutsideClick = () => {
    setShowNotifications(false);
    setShowProfile(false);
  };

  return (
    <div onClick={handleOutsideClick}>
      <ResponsiveLayout
        navigation={
          <ResponsiveNavigation
            currentPage={currentPage}
            activeTab={activeTab}
            onNavigate={handleNavigate}
            userType={user.userType}
            onNavigateToProfile={handleNavigateToProfile}
            onLogout={onLogout}
          />
        }
        className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-orange-50"
      >
        {renderPageContent()}
      </ResponsiveLayout>

      {/* Notification System */}
      <NotificationSystem
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
      />
    </div>
  );
};

export default Dashboard;
