import { useState, useEffect } from 'react';
import { X, User, Package, Plus, Trash2, Search } from 'lucide-react';
import { liveDataService } from '../../services/liveDataService';
import { syncOrderCreated } from '../../services/realTimeService';

interface Customer {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  company?: string;
  address?: any;
  userType?: 'client' | 'reseller';
}

interface Product {
  id: string;
  title: string;
  price: number;
  stock: number;
  image?: string;
  isActive?: boolean;
  sku?: string;
  description?: string;
  brand?: string;
}

interface OrderItem {
  productId: string;
  productTitle: string;
  quantity: number;
  price: number;
  total: number;
}

interface CreateOrderModalProps {
  onClose: () => void;
  onOrderCreated: () => void;
}

const CreateOrderModal = ({ onClose, onOrderCreated }: CreateOrderModalProps) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [step, setStep] = useState<'customer' | 'products' | 'review'>('customer');
  const [paymentMethod, setPaymentMethod] = useState('cash');

  useEffect(() => {
    loadCustomers();
    loadProducts();
  }, []);

  const loadCustomers = async () => {
    try {
      setLoadingCustomers(true);
      console.log('CreateOrderModal: Loading customers...');

      // Check if liveDataService is available
      if (!liveDataService || typeof liveDataService.getAllCustomers !== 'function') {
        throw new Error('liveDataService is not available or getAllCustomers method is missing');
      }

      const customerUsers = await liveDataService.getAllCustomers();
      console.log('CreateOrderModal: Customers loaded:', customerUsers?.length || 0, customerUsers);

      if (!Array.isArray(customerUsers)) {
        throw new Error('getAllCustomers did not return an array');
      }

      // Transform the data to match our Customer interface
      const transformedCustomers = customerUsers.map(user => ({
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        phone: user.phone,
        company: user.company_name,
        address: user.company_address,
        userType: user.user_type as 'client' | 'reseller'
      }));

      console.log('CreateOrderModal: Transformed customers:', transformedCustomers.length, transformedCustomers);

      setCustomers(transformedCustomers);

      if (transformedCustomers.length === 0) {
        console.warn('CreateOrderModal: No customers found with client or reseller roles');
        console.warn('CreateOrderModal: This might indicate no users exist with client/reseller user_type in the database');
      }
    } catch (error) {
      console.error('CreateOrderModal: Error loading customers:', error);
      setCustomers([]); // Set empty array on error

      // Show user-friendly error message
      alert(`Failed to load customers: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoadingCustomers(false);
    }
  };

  const loadProducts = async () => {
    try {
      setLoadingProducts(true);
      console.log('CreateOrderModal: Loading products...');

      const allProducts = await liveDataService.getAllProducts();
      console.log('CreateOrderModal: All products loaded:', allProducts?.length || 0, allProducts);

      if (!Array.isArray(allProducts)) {
        throw new Error('getAllProducts did not return an array');
      }

      // Filter for active products with stock > 0
      const activeProducts = allProducts.filter(product => {
        const isActive = product.isActive !== false; // Default to true if undefined
        const hasStock = (product.stock || 0) > 0;
        return isActive && hasStock;
      });

      console.log('CreateOrderModal: Active products with stock:', activeProducts.length, activeProducts);

      setProducts(activeProducts);

      if (activeProducts.length === 0) {
        console.warn('CreateOrderModal: No active products with stock found');
        if (allProducts.length > 0) {
          console.warn('CreateOrderModal: Total products available:', allProducts.length);
          console.warn('CreateOrderModal: Sample product structure:', allProducts[0]);
        }
      }
    } catch (error) {
      console.error('CreateOrderModal: Error loading products:', error);
      setProducts([]); // Set empty array on error

      // Show user-friendly error message
      alert(`Failed to load products: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoadingProducts(false);
    }
  };

  const filteredCustomers = customers.filter(customer => {
    // If no search term, show all customers
    if (!customerSearch.trim()) {
      return true;
    }

    const searchLower = customerSearch.toLowerCase().trim();
    const nameMatch = customer.full_name?.toLowerCase().includes(searchLower) || false;
    const emailMatch = customer.email?.toLowerCase().includes(searchLower) || false;
    const companyMatch = customer.company?.toLowerCase().includes(searchLower) || false;
    const phoneMatch = customer.phone?.toLowerCase().includes(searchLower) || false;

    return nameMatch || emailMatch || companyMatch || phoneMatch;
  });

  // Debug logging for search functionality
  console.log('CreateOrderModal: Customer search debug:', {
    totalCustomers: customers.length,
    searchTerm: customerSearch,
    filteredCount: filteredCustomers.length,
    loadingCustomers
  });

  const filteredProducts = products.filter(product => {
    // If no search term, show all products
    if (!productSearch.trim()) {
      return true;
    }

    const searchLower = productSearch.toLowerCase().trim();
    const titleMatch = product.title?.toLowerCase().includes(searchLower) || false;
    const skuMatch = product.sku?.toLowerCase().includes(searchLower) || false;
    const brandMatch = product.brand?.toLowerCase().includes(searchLower) || false;
    const descriptionMatch = product.description?.toLowerCase().includes(searchLower) || false;

    return titleMatch || skuMatch || brandMatch || descriptionMatch;
  });

  const addProductToOrder = (product: Product) => {
    const existingItem = orderItems.find(item => item.productId === product.id);

    // Determine the correct price based on customer type
    const customerPrice = selectedCustomer?.userType === 'reseller'
      ? (product.resellerPrice || product.price)
      : product.price;

    if (existingItem) {
      if (existingItem.quantity < product.stock) {
        setOrderItems(prev => prev.map(item =>
          item.productId === product.id
            ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
            : item
        ));
      } else {
        alert('Cannot add more items. Stock limit reached.');
      }
    } else {
      const newItem: OrderItem = {
        productId: product.id,
        productTitle: product.title,
        quantity: 1,
        price: customerPrice,
        total: customerPrice
      };
      setOrderItems(prev => [...prev, newItem]);
    }
  };

  const updateItemQuantity = (productId: string, quantity: number) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    if (quantity <= 0) {
      removeItem(productId);
      return;
    }

    if (quantity > product.stock) {
      alert('Quantity cannot exceed available stock');
      return;
    }

    setOrderItems(prev => prev.map(item =>
      item.productId === productId
        ? { ...item, quantity, total: quantity * item.price }
        : item
    ));
  };

  const removeItem = (productId: string) => {
    setOrderItems(prev => prev.filter(item => item.productId !== productId));
  };

  const calculateTotal = () => {
    return orderItems.reduce((sum, item) => sum + item.total, 0);
  };

  const handleCreateOrder = async () => {
    if (!selectedCustomer || orderItems.length === 0) {
      alert('Please select a customer and add at least one product');
      return;
    }

    setLoading(true);
    try {
      const orderData = {
        customer_id: selectedCustomer.id,
        status: 'pending',
        payment_method: paymentMethod,
        payment_status: 'pending',
        total: calculateTotal(),
        items: orderItems.map(item => ({
          product_id: item.productId,
          quantity: item.quantity,
          price: item.price
        }))
      };

      const newOrder = await liveDataService.createOrder(orderData);
      
      if (newOrder) {
        // Emit real-time events
        syncOrderCreated(newOrder.id, newOrder);
        
        alert('Order created successfully!');
        onOrderCreated();
        onClose();
      } else {
        throw new Error('Failed to create order');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Error creating order: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Create New Order</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Step Indicator */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${step === 'customer' ? 'text-orange-600' : step === 'products' || step === 'review' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'customer' ? 'bg-orange-100' : step === 'products' || step === 'review' ? 'bg-green-100' : 'bg-gray-100'}`}>
                1
              </div>
              <span>Select Customer</span>
            </div>
            <div className="flex-1 h-px bg-gray-300"></div>
            <div className={`flex items-center space-x-2 ${step === 'products' ? 'text-orange-600' : step === 'review' ? 'text-green-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'products' ? 'bg-orange-100' : step === 'review' ? 'bg-green-100' : 'bg-gray-100'}`}>
                2
              </div>
              <span>Add Products</span>
            </div>
            <div className="flex-1 h-px bg-gray-300"></div>
            <div className={`flex items-center space-x-2 ${step === 'review' ? 'text-orange-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${step === 'review' ? 'bg-orange-100' : 'bg-gray-100'}`}>
                3
              </div>
              <span>Review & Create</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'customer' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Select Customer</h3>
              
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search customers by name, email, phone, or company..."
                    value={customerSearch}
                    onChange={(e) => setCustomerSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                {/* Debug Info & Reload Button */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    {loadingCustomers ? 'Loading...' : `${customers.length} total customers, ${filteredCustomers.length} filtered`}
                  </span>
                  <div className="flex space-x-1">
                    <button
                      onClick={loadCustomers}
                      disabled={loadingCustomers}
                      className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-xs disabled:opacity-50"
                    >
                      Reload
                    </button>
                    {customers.length === 0 && (
                      <button
                        onClick={async () => {
                          console.log('🔍 Running customer diagnosis...');
                          const { diagnoseCustomerIssues } = await import('../../utils/diagnoseCustomerIssues');
                          await diagnoseCustomerIssues();
                        }}
                        className="px-2 py-1 bg-blue-100 hover:bg-blue-200 rounded text-xs text-blue-700"
                      >
                        Debug
                      </button>
                    )}
                  </div>
                </div>
              </div>

              <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                {loadingCustomers ? (
                  <div className="p-4 text-center text-gray-500">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600"></div>
                      <span>Loading customers...</span>
                    </div>
                  </div>
                ) : customers.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <div className="space-y-2">
                      <p>No customers found in database</p>
                      <p className="text-xs">Make sure there are users with 'client' or 'reseller' roles</p>
                    </div>
                  </div>
                ) : filteredCustomers.length === 0 && customerSearch.trim() ? (
                  <div className="p-4 text-center text-gray-500">
                    <div className="space-y-2">
                      <p>No customers found matching "{customerSearch}"</p>
                      <p className="text-xs">Try searching by name, email, phone, or company</p>
                    </div>
                  </div>
                ) : (
                  filteredCustomers.map(customer => (
                  <div
                    key={customer.id}
                    onClick={() => setSelectedCustomer(customer)}
                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedCustomer?.id === customer.id ? 'bg-orange-50 border-orange-200' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-teal-600 to-amber-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold text-sm">
                          {customer.full_name?.charAt(0)?.toUpperCase() || 'U'}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {customer.full_name || 'Unknown Name'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {customer.email || 'No email'}
                        </div>
                        {customer.company && (
                          <div className="text-sm text-blue-600 font-medium">
                            {customer.company}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 capitalize">
                          {customer.userType || 'Unknown role'}
                        </div>
                      </div>
                      {selectedCustomer?.id === customer.id && (
                        <div className="text-orange-600">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </div>
                  ))
                )}
              </div>



              {selectedCustomer && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900">Selected Customer:</h4>
                  <p className="text-green-800">{selectedCustomer.full_name} ({selectedCustomer.email})</p>
                </div>
              )}
            </div>
          )}

          {step === 'products' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Add Products</h3>
              
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search products by name, SKU, brand, or description..."
                  value={productSearch}
                  onChange={(e) => setProductSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>

              {/* Product Status Info */}
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>
                  {loadingProducts ? 'Loading products...' : `${products.length} total products, ${filteredProducts.length} filtered`}
                </span>
                <div className="flex space-x-1">
                  <button
                    onClick={loadProducts}
                    disabled={loadingProducts}
                    className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-xs disabled:opacity-50"
                  >
                    Reload
                  </button>
                  {products.length === 0 && !loadingProducts && (
                    <button
                      onClick={async () => {
                        console.log('🔍 Running product diagnosis...');
                        console.log('Products state:', products);
                        console.log('Loading state:', loadingProducts);
                        console.log('Filtered products:', filteredProducts);
                      }}
                      className="px-2 py-1 bg-blue-100 hover:bg-blue-200 rounded text-xs text-blue-700"
                    >
                      Debug
                    </button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Available Products</h4>
                  <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                    {loadingProducts ? (
                      <div className="p-4 text-center text-gray-500">
                        <div className="flex items-center justify-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600"></div>
                          <span>Loading products...</span>
                        </div>
                      </div>
                    ) : products.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        <div className="space-y-2">
                          <p>No products found in database</p>
                          <p className="text-xs">Make sure there are active products with stock &gt; 0</p>
                        </div>
                      </div>
                    ) : filteredProducts.length === 0 && productSearch.trim() ? (
                      <div className="p-4 text-center text-gray-500">
                        <div className="space-y-2">
                          <p>No products found matching "{productSearch}"</p>
                          <p className="text-xs">Try searching by product name</p>
                        </div>
                      </div>
                    ) : (
                      filteredProducts.map(product => (
                        <div
                          key={product.id}
                          className="p-3 border-b border-gray-100 hover:bg-gray-50"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">{product.title}</div>
                              <div className="text-sm text-gray-500">
                                {selectedCustomer?.userType === 'reseller' ? (
                                  <>
                                    <span className="font-medium text-teal-600">
                                      {(product.resellerPrice || product.price)?.toFixed(2)} Dh
                                    </span>
                                    {product.resellerPrice && product.resellerPrice < product.price && (
                                      <span className="ml-2 line-through text-gray-400">
                                        {product.price?.toFixed(2)} Dh
                                      </span>
                                    )}
                                  </>
                                ) : (
                                  <span>{product.price?.toFixed(2)} Dh</span>
                                )} • Stock: {product.stock}
                              </div>
                              {product.sku && (
                                <div className="text-xs text-gray-400">SKU: {product.sku}</div>
                              )}
                            </div>
                            <button
                              onClick={() => addProductToOrder(product)}
                              className="bg-orange-500 text-white p-1 rounded hover:bg-orange-600 transition-colors"
                              title={`Add ${product.title} to order`}
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Order Items ({orderItems.length})</h4>
                  <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                    {orderItems.map(item => (
                      <div key={item.productId} className="p-3 border-b border-gray-100">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{item.productTitle}</div>
                            <div className="text-sm text-gray-500">
                              {item.price.toFixed(2)} Dh each
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateItemQuantity(item.productId, parseInt(e.target.value) || 0)}
                              className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                            />
                            <span className="text-sm font-medium">{item.total.toFixed(2)} Dh</span>
                            <button
                              onClick={() => removeItem(item.productId)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                    {orderItems.length === 0 && (
                      <div className="p-4 text-center text-gray-500">
                        No items added yet
                      </div>
                    )}
                  </div>
                  {orderItems.length > 0 && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                      <div className="flex justify-between font-medium">
                        <span>Total:</span>
                        <span>{calculateTotal().toFixed(2)} Dh</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {step === 'review' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Review Order</h3>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Customer Information</h4>
                <p>{selectedCustomer?.full_name}</p>
                <p className="text-sm text-gray-600">{selectedCustomer?.email}</p>
                {selectedCustomer?.company && (
                  <p className="text-sm text-gray-600">{selectedCustomer.company}</p>
                )}
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Order Items</h4>
                <div className="space-y-2">
                  {orderItems.map(item => (
                    <div key={item.productId} className="flex justify-between">
                      <span>{item.productTitle} x {item.quantity}</span>
                      <span>{item.total.toFixed(2)} Dh</span>
                    </div>
                  ))}
                  <div className="border-t pt-2 font-medium">
                    <div className="flex justify-between">
                      <span>Total:</span>
                      <span>{calculateTotal().toFixed(2)} Dh</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Method Selection */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Payment Method</h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="cash"
                      checked={paymentMethod === 'cash'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="text-teal-600 focus:ring-teal-500"
                    />
                    <span className="text-gray-900">Cash</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="bank_transfer"
                      checked={paymentMethod === 'bank_transfer'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="text-teal-600 focus:ring-teal-500"
                    />
                    <span className="text-gray-900">Bank Transfer</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="check"
                      checked={paymentMethod === 'check'}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="text-teal-600 focus:ring-teal-500"
                    />
                    <span className="text-gray-900">Check</span>
                  </label>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div>
            {step !== 'customer' && (
              <button
                onClick={() => {
                  if (step === 'products') setStep('customer');
                  if (step === 'review') setStep('products');
                }}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors"
              >
                Back
              </button>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors"
            >
              Cancel
            </button>
            
            {step === 'customer' && (
              <button
                onClick={() => setStep('products')}
                disabled={!selectedCustomer}
                className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            )}
            
            {step === 'products' && (
              <button
                onClick={() => setStep('review')}
                disabled={orderItems.length === 0}
                className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Review Order
              </button>
            )}
            
            {step === 'review' && (
              <button
                onClick={handleCreateOrder}
                disabled={loading}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Order'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateOrderModal;
