import { useState, useEffect } from 'react';
import { ShoppingCart, Package, Star, Filter, Search, Grid, List, Bell, User, Shield, Heart } from 'lucide-react';
import { useMobileUtils } from '../../hooks/use-mobile';
import ProductGrid from '../products/ProductGrid';
import Cart from '../cart/Cart';
import OrderHistory from '../orders/OrderHistory';
import ProfileManagement from '../profile/ProfileManagement';
import NotificationBell from '../ui/NotificationBell';
import AdvancedSearch, { SearchFilters as AdvancedSearchFilters } from '../search/AdvancedSearch';
import { SearchFilters } from '../products/ProductGrid';
import RecentlyViewed from '../search/RecentlyViewed';
import SecurityDashboard from '../security/SecurityDashboard';
import BestSellingProducts from '../dashboard/BestSellingProducts';
import WishlistDashboard from '../wishlist/WishlistDashboard';
import { getActiveCategories } from '../../services/liveCategoryService';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';

interface ResellerDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
  dashboardNavigateRef?: React.MutableRefObject<((page: string) => void) | null>;
}

const ResellerDashboard = ({ user, onNavigateFromHeader, dashboardNavigateRef }: ResellerDashboardProps) => {
  const { isMobile, isSmallMobile, shouldUseTouchUI } = useMobileUtils();
  
  // State management
  const [activeTab, setActiveTab] = useState('products');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showCart, setShowCart] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [cart, setCart] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [brands, setBrands] = useState<string[]>([]);
  
  // Search and filter states
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchTerm: '',
    category: '',
    brand: '',
    priceRange: [0, 1000],
    availability: 'all',
    sortBy: 'relevance'
  });

  // Initialize dashboard
  useEffect(() => {
    // Set up navigation reference
    if (dashboardNavigateRef) {
      dashboardNavigateRef.current = handleNavigate;
    }

    // Load initial data
    loadCategories();
    loadBrands();

    // Get initial tab from URL or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const tabFromUrl = urlParams.get('tab');
    const savedTab = localStorage.getItem('reseller_active_tab');
    
    if (tabFromUrl) {
      setActiveTab(tabFromUrl);
    } else if (savedTab) {
      setActiveTab(savedTab);
    } else {
      setActiveTab('products'); // Default to products tab
    }

    // Set up real-time subscriptions
    const unsubscribeProduct = realTimeService.subscribe('product-updated', (data) => {
      console.log('Product updated:', data);
      // Handle product updates
    });

    const unsubscribeStock = realTimeService.subscribe('stock-updated', (data) => {
      console.log('Stock updated:', data);
      // Handle stock updates
    });

    const unsubscribePrice = realTimeService.subscribe('price-updated', (data) => {
      console.log('Price updated:', data);
      // Handle price updates
    });

    const unsubscribeOrder = realTimeService.subscribe('order-updated', (data) => {
      console.log('Order updated:', data);
      // Handle order updates
    });

    const unsubscribeImage = realTimeService.subscribe('product-image-updated', (data) => {
      console.log('Product image updated:', data);
      // Handle image updates
    });

    const unsubscribeReview = realTimeService.subscribe('review-updated', (data) => {
      console.log('Review updated:', data);
      // Handle review updates
    });

    // Subscribe to Supabase real-time updates
    const productSubscription = liveDataService.subscribeToProducts((payload) => {
      console.log('Real-time product update:', payload);
      // Handle real-time product updates
    });

    const orderSubscription = liveDataService.subscribeToOrders((payload) => {
      console.log('Real-time order update:', payload);
      // Handle real-time order updates
    });

    return () => {
      unsubscribeProduct();
      unsubscribeStock();
      unsubscribePrice();
      unsubscribeOrder();
      unsubscribeImage();
      unsubscribeReview();
      productSubscription.unsubscribe();
      orderSubscription.unsubscribe();
    };
  }, []);

  const loadCategories = async () => {
    try {
      const categoriesData = await getActiveCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadBrands = async () => {
    try {
      const products = await liveDataService.getAllProducts();
      const uniqueBrands = [...new Set(products.map(p => p.brand).filter(Boolean))];
      setBrands(uniqueBrands);
    } catch (error) {
      console.error('Error loading brands:', error);
    }
  };

  // Cart management
  const addToCart = (product: any) => {
    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { ...product, quantity: 1 }]);
    }
  };

  const updateCartQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
    } else {
      setCart(cart.map(item =>
        item.id === productId ? { ...item, quantity } : item
      ));
    }
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.id !== productId));
  };

  const clearCart = () => {
    setCart([]);
  };

  const cartTotal = cart.reduce((sum, item) => {
    // Always use reseller price for resellers
    const price = item.resellerPrice || item.price;
    return sum + (price * item.quantity);
  }, 0);

  // User update handler
  const handleUserUpdate = (updatedUser: any) => {
    console.log('User updated:', updatedUser);
    // Handle user updates if needed
  };

  // Navigation handler
  const handleNavigate = (page: string) => {
    console.log('ResellerDashboard: handleNavigate called with:', page);

    // Map external navigation to internal tabs
    let targetTab = page;
    if (page === 'dashboard') {
      targetTab = 'products'; // Map "Shop" (dashboard) to products tab
      console.log('ResellerDashboard: Mapping dashboard to products tab');
    }

    console.log('ResellerDashboard: Setting active tab to:', targetTab);
    setActiveTab(targetTab);

    // Update URL parameters
    const url = new URL(window.location.href);
    url.searchParams.set('tab', targetTab);

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('reseller_active_tab', targetTab);
  };

  // Advanced search filters conversion
  const convertToAdvancedSearchFilters = (filters: SearchFilters): AdvancedSearchFilters => ({
    searchTerm: filters.searchTerm,
    categories: filters.category ? [filters.category] : [],
    brands: filters.brand ? [filters.brand] : [],
    priceRange: filters.priceRange,
    availability: filters.availability,
    sortBy: filters.sortBy,
    dateRange: null
  });

  const convertFromAdvancedSearchFilters = (filters: AdvancedSearchFilters): SearchFilters => ({
    searchTerm: filters.searchTerm,
    category: filters.categories[0] || '',
    brand: filters.brands[0] || '',
    priceRange: filters.priceRange,
    availability: filters.availability,
    sortBy: filters.sortBy
  });

  return (
    <div className="space-y-6">
      {/* Welcome Section - Enhanced for Resellers */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-white">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold mb-2">
              Welcome back, {user.fullName}!
            </h2>
            <p className="text-lg opacity-90">
              Reseller Account
              <span className="ml-2 bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm">
                Special Pricing Available
              </span>
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Notification Bell */}
            <NotificationBell userId={user.id} />
          </div>
        </div>
      </div>

      {/* Navigation Tabs - Enhanced Mobile Responsive */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className={`
          ${isMobile ? 'overflow-x-auto scrollbar-hide' : 'flex'}
          ${isMobile ? 'p-2' : 'p-1'}
        `}>
          <div className={`
            flex
            ${isMobile ? 'space-x-2 min-w-max' : 'space-x-1 w-full'}
          `}>
            {[
              { id: 'products', label: 'Products', icon: Package, shortLabel: 'Products' },
              { id: 'wishlist', label: 'Wishlist', icon: Heart, shortLabel: 'Wishlist' },
              { id: 'orders', label: 'My Orders', icon: ShoppingCart, shortLabel: 'Orders' },
              { id: 'security', label: 'Security', icon: Shield, shortLabel: 'Security' },
              { id: 'profile', label: 'Profile Settings', icon: User, shortLabel: 'Profile' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  ${isMobile ? 'px-3 py-2 min-w-[80px]' : 'flex-1 px-4 py-3'}
                  ${shouldUseTouchUI ? 'min-h-[44px]' : ''}
                  rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2
                  ${activeTab === tab.id
                    ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-teal-600 hover:bg-teal-50'
                  }
                `}
              >
                <tab.icon className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                <span className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {isMobile ? tab.shortLabel : tab.label}
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Products Tab */}
      {activeTab === 'products' && (
        <div className="space-y-6">
          {/* Best Selling Products Section */}
          <BestSellingProducts
            userType="reseller"
            customerId={user.id}
            customerName={user.fullName}
            onAddToCart={addToCart}
            onViewProduct={(productId) => console.log('View product:', productId)}
            onAddToWishlist={(product) => console.log('Add to wishlist:', product)}
          />

          {/* Search and Filter Controls */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'items-center justify-between'}`}>
              {/* Search Bar */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchFilters.searchTerm}
                  onChange={(e) => setSearchFilters({ ...searchFilters, searchTerm: e.target.value })}
                  className={`w-full pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
                    shouldUseTouchUI ? 'py-3 text-base' : 'py-2 text-sm'
                  }`}
                />
              </div>

              {/* Action Buttons */}
              <div className={`flex ${isMobile ? 'justify-between' : 'space-x-3'}`}>
                <button
                  onClick={() => setShowAdvancedSearch(true)}
                  className={`${shouldUseTouchUI ? 'px-4 py-3 min-h-[44px]' : 'px-4 py-2'} bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2`}
                >
                  <Filter className="h-4 w-4" />
                  <span className="text-sm">Filters</span>
                </button>

                <div className="flex space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`${shouldUseTouchUI ? 'p-3 min-h-[44px] min-w-[44px]' : 'p-2'} rounded-lg transition-colors ${
                      viewMode === 'grid' ? 'bg-teal-100 text-teal-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <Grid className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`${shouldUseTouchUI ? 'p-3 min-h-[44px] min-w-[44px]' : 'p-2'} rounded-lg transition-colors ${
                      viewMode === 'list' ? 'bg-teal-100 text-teal-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>

                <button
                  onClick={() => setShowCart(true)}
                  className={`${shouldUseTouchUI ? 'px-4 py-3 min-h-[44px]' : 'px-4 py-2'} bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-lg hover:from-teal-600 hover:to-teal-700 transition-colors flex items-center space-x-2 relative`}
                >
                  <ShoppingCart className="h-4 w-4" />
                  <span className="text-sm">Cart</span>
                  {cart.length > 0 && (
                    <span className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {cart.length}
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Products Grid/List */}
          <ProductGrid
            viewMode={viewMode}
            searchFilters={searchFilters}
            userType="reseller"
            customerId={user.id}
            customerName={user.fullName}
            onAddToCart={addToCart}
            onAddToWishlist={(product) => console.log('Add to wishlist:', product)}
          />
        </div>
      )}

      {/* Wishlist Tab */}
      {activeTab === 'wishlist' && (
        <WishlistDashboard
          customerId={user.id}
          customerName={user.fullName}
          userType="reseller"
          onAddToCart={addToCart}
        />
      )}

      {/* Orders Tab */}
      {activeTab === 'orders' && (
        <OrderHistory customerId={user.id} />
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <SecurityDashboard userRole="reseller" />
      )}

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
      )}

      {/* Modals */}
      {showCart && (
        <Cart
          isOpen={showCart}
          items={cart}
          userType="reseller"
          user={{
            id: user.id,
            fullName: user.fullName,
            email: user.email
          }}
          onClose={() => setShowCart(false)}
          onUpdateQuantity={updateCartQuantity}
          onRemoveItem={removeFromCart}
          onClearCart={clearCart}
        />
      )}

      {showAdvancedSearch && (
        <AdvancedSearch
          filters={convertToAdvancedSearchFilters(searchFilters)}
          onFiltersChange={(filters) => setSearchFilters(convertFromAdvancedSearchFilters(filters))}
          categories={categories.map(cat => cat.name || cat.title || cat.id)}
          brands={brands}
          priceRange={[0, 1000]}
          isOpen={showAdvancedSearch}
          onClose={() => setShowAdvancedSearch(false)}
        />
      )}
    </div>
  );
};

export default ResellerDashboard;
