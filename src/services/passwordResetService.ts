import { supabase } from '../integrations/supabase/client';
import { liveDataService } from './liveDataService';

export interface PasswordResetResult {
  success: boolean;
  message: string;
  error?: string;
}

export interface SMTPConfig {
  smtp_host: string;
  smtp_port: number;
  smtp_username: string;
  smtp_password: string;
  smtp_secure: boolean;
  from_email: string;
  from_name: string;
}

class PasswordResetService {
  /**
   * Get SMTP configuration from system settings
   */
  private async getSMTPConfig(): Promise<SMTPConfig | null> {
    try {
      const { data, error } = await supabase
        .from('system_settings')
        .select('key, value')
        .eq('category', 'email');

      if (error) {
        console.error('Error fetching SMTP config:', error);
        return null;
      }

      const config: Partial<SMTPConfig> = {};
      data?.forEach(setting => {
        if (setting.key.startsWith('smtp_') || setting.key === 'from_email' || setting.key === 'from_name') {
          config[setting.key as keyof SMTPConfig] = setting.value;
        }
      });

      // Validate required fields
      if (!config.smtp_host || !config.smtp_username || !config.from_email) {
        console.warn('SMTP configuration incomplete');
        return null;
      }

      return config as SMTPConfig;
    } catch (error) {
      console.error('Error getting SMTP config:', error);
      return null;
    }
  }

  /**
   * Send password reset email using custom SMTP or fallback to Supabase
   */
  async sendPasswordResetEmail(userEmail: string, userId?: string): Promise<PasswordResetResult> {
    try {
      console.log('🔄 Sending password reset email to:', userEmail);

      // First, try to get SMTP configuration
      const smtpConfig = await this.getSMTPConfig();

      if (smtpConfig && this.isValidSMTPConfig(smtpConfig)) {
        // Use custom SMTP service
        return await this.sendCustomPasswordResetEmail(userEmail, smtpConfig, userId);
      } else {
        // Fallback to Supabase auth service
        console.warn('⚠️  SMTP not configured, using Supabase auth service');
        return await this.sendSupabasePasswordResetEmail(userEmail);
      }
    } catch (error) {
      console.error('❌ Error in sendPasswordResetEmail:', error);
      return {
        success: false,
        message: 'Failed to send password reset email',
        error: (error as Error).message
      };
    }
  }

  /**
   * Validate SMTP configuration
   */
  private isValidSMTPConfig(config: SMTPConfig): boolean {
    return !!(
      config.smtp_host &&
      config.smtp_username &&
      config.smtp_password &&
      config.from_email &&
      config.smtp_port > 0
    );
  }

  /**
   * Send password reset email using custom SMTP
   */
  private async sendCustomPasswordResetEmail(
    userEmail: string, 
    smtpConfig: SMTPConfig, 
    userId?: string
  ): Promise<PasswordResetResult> {
    try {
      // Generate a secure reset token
      const resetToken = this.generateResetToken();
      const resetUrl = `${window.location.origin}/reset-password?token=${resetToken}&email=${encodeURIComponent(userEmail)}`;

      // Store reset token in database with expiration
      if (userId) {
        await this.storeResetToken(userId, resetToken);
      }

      // Create email content
      const emailContent = this.createPasswordResetEmailTemplate(userEmail, resetUrl);

      // Send email using the system's email service
      const emailSent = await this.sendEmailViaSMTP(smtpConfig, userEmail, emailContent);

      if (emailSent) {
        console.log('✅ Custom SMTP password reset email sent successfully');
        return {
          success: true,
          message: 'Password reset email sent successfully. Please check your inbox and follow the instructions.'
        };
      } else {
        throw new Error('Failed to send email via SMTP');
      }
    } catch (error) {
      console.error('❌ Error sending custom password reset email:', error);
      // Fallback to Supabase
      return await this.sendSupabasePasswordResetEmail(userEmail);
    }
  }

  /**
   * Send password reset email using Supabase auth service
   */
  private async sendSupabasePasswordResetEmail(userEmail: string): Promise<PasswordResetResult> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(userEmail, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) {
        console.error('❌ Supabase password reset error:', error);
        
        // Provide user-friendly error messages
        if (error.message.includes('Email not confirmed')) {
          return {
            success: false,
            message: 'Please confirm your email address first before resetting your password.',
            error: error.message
          };
        } else if (error.message.includes('User not found')) {
          return {
            success: false,
            message: 'No account found with this email address.',
            error: error.message
          };
        } else {
          return {
            success: false,
            message: 'Failed to send password reset email. Please try again or contact support.',
            error: error.message
          };
        }
      }

      console.log('✅ Supabase password reset email sent successfully');
      return {
        success: true,
        message: 'Password reset email sent successfully. Please check your inbox and follow the instructions.'
      };
    } catch (error) {
      console.error('❌ Error with Supabase password reset:', error);
      return {
        success: false,
        message: 'Failed to send password reset email. Please try again.',
        error: (error as Error).message
      };
    }
  }

  /**
   * Generate a secure reset token
   */
  private generateResetToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Store reset token in database with expiration
   */
  private async storeResetToken(userId: string, token: string): Promise<void> {
    try {
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      const { error } = await supabase
        .from('password_reset_tokens')
        .upsert({
          user_id: userId,
          token: token,
          expires_at: expiresAt.toISOString(),
          used: false,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error storing reset token:', error);
      }
    } catch (error) {
      console.error('Error storing reset token:', error);
    }
  }

  /**
   * Create password reset email template
   */
  private createPasswordResetEmailTemplate(userEmail: string, resetUrl: string) {
    return {
      subject: 'Reset Your YalaOffice Password',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #0d9488 0%, #f59e0b 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: white; margin: 0; font-size: 28px;">YalaOffice</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Password Reset Request</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
            <h2 style="color: #0d9488; margin-top: 0;">Reset Your Password</h2>
            
            <p>Hello,</p>
            
            <p>We received a request to reset the password for your YalaOffice account associated with <strong>${userEmail}</strong>.</p>
            
            <p>Click the button below to reset your password:</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" 
                 style="background: linear-gradient(135deg, #0d9488 0%, #f59e0b 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 8px; 
                        font-weight: bold; 
                        display: inline-block;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                Reset Password
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <a href="${resetUrl}" style="color: #0d9488; word-break: break-all;">${resetUrl}</a>
            </p>
            
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p style="margin: 0; color: #856404; font-size: 14px;">
                <strong>Security Notice:</strong> This link will expire in 24 hours. If you didn't request this password reset, please ignore this email or contact support if you have concerns.
              </p>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              Best regards,<br>
              The YalaOffice Team
            </p>
          </div>
          
          <div style="text-align: center; padding: 20px; color: #666; font-size: 12px;">
            <p>© ${new Date().getFullYear()} YalaOffice. All rights reserved.</p>
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </body>
        </html>
      `,
      text: `
        YalaOffice - Password Reset Request
        
        Hello,
        
        We received a request to reset the password for your YalaOffice account associated with ${userEmail}.
        
        Click the link below to reset your password:
        ${resetUrl}
        
        This link will expire in 24 hours. If you didn't request this password reset, please ignore this email.
        
        Best regards,
        The YalaOffice Team
      `
    };
  }

  /**
   * Send email via SMTP using EmailJS or similar web-based service
   */
  private async sendEmailViaSMTP(
    smtpConfig: SMTPConfig,
    toEmail: string,
    emailContent: { subject: string; html: string; text: string }
  ): Promise<boolean> {
    try {
      console.log('📧 Sending email via web service:', {
        host: smtpConfig.smtp_host,
        port: smtpConfig.smtp_port,
        from: `${smtpConfig.from_name} <${smtpConfig.from_email}>`,
        to: toEmail,
        subject: emailContent.subject
      });

      // Try to use a web-based email service
      const emailSent = await this.sendViaWebEmailService(smtpConfig, toEmail, emailContent);

      if (emailSent) {
        console.log('✅ Email sent successfully via web service');
        return true;
      }

      // Fallback: Log email details for manual processing
      console.log('⚠️  Web email service failed, logging email for manual processing');
      await this.logEmailForManualProcessing(smtpConfig, toEmail, emailContent);

      // Return true to indicate the email was "processed" (even if manually)
      return true;
    } catch (error) {
      console.error('Error sending email via SMTP:', error);
      return false;
    }
  }

  /**
   * Send email via web-based email service (EmailJS, Formspree, etc.)
   */
  private async sendViaWebEmailService(
    smtpConfig: SMTPConfig,
    toEmail: string,
    emailContent: { subject: string; html: string; text: string }
  ): Promise<boolean> {
    try {
      // Option 1: Use EmailJS (requires setup in EmailJS dashboard)
      // This would require EmailJS service ID, template ID, and public key

      // Option 2: Use a custom email API endpoint
      // This would require a backend service to handle SMTP

      // Option 3: Use Supabase Edge Functions for email sending
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          to: toEmail,
          from: `${smtpConfig.from_name} <${smtpConfig.from_email}>`,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          smtp_config: {
            host: smtpConfig.smtp_host,
            port: smtpConfig.smtp_port,
            username: smtpConfig.smtp_username,
            password: smtpConfig.smtp_password,
            secure: smtpConfig.smtp_secure
          }
        }
      });

      if (error) {
        console.log('⚠️  Supabase Edge Function not available, trying alternative method');
        return false;
      }

      console.log('✅ Email sent via Supabase Edge Function');
      return true;
    } catch (error) {
      console.log('⚠️  Web email service failed:', error);
      return false;
    }
  }

  /**
   * Log email for manual processing when automated sending fails
   */
  private async logEmailForManualProcessing(
    smtpConfig: SMTPConfig,
    toEmail: string,
    emailContent: { subject: string; html: string; text: string }
  ): Promise<void> {
    try {
      // Store email in database for manual processing
      const { error } = await supabase
        .from('email_queue')
        .insert({
          to_email: toEmail,
          from_email: smtpConfig.from_email,
          from_name: smtpConfig.from_name,
          subject: emailContent.subject,
          html_content: emailContent.html,
          text_content: emailContent.text,
          status: 'pending',
          smtp_config: smtpConfig,
          created_at: new Date().toISOString(),
          attempts: 0
        });

      if (error) {
        console.error('Failed to log email for manual processing:', error);
      } else {
        console.log('📝 Email logged for manual processing');
      }
    } catch (error) {
      console.error('Error logging email:', error);
    }
  }

  /**
   * Test SMTP configuration
   */
  async testSMTPConfiguration(testEmail: string): Promise<PasswordResetResult> {
    try {
      const smtpConfig = await this.getSMTPConfig();
      
      if (!smtpConfig) {
        return {
          success: false,
          message: 'SMTP configuration not found. Please configure SMTP settings first.',
          error: 'No SMTP configuration'
        };
      }

      if (!this.isValidSMTPConfig(smtpConfig)) {
        return {
          success: false,
          message: 'SMTP configuration is incomplete. Please check all required fields.',
          error: 'Invalid SMTP configuration'
        };
      }

      const testEmailContent = {
        subject: 'YalaOffice SMTP Test Email',
        html: `
          <h2>SMTP Test Successful</h2>
          <p>This is a test email to verify your SMTP configuration is working correctly.</p>
          <p><strong>Configuration:</strong></p>
          <ul>
            <li>Host: ${smtpConfig.smtp_host}</li>
            <li>Port: ${smtpConfig.smtp_port}</li>
            <li>Secure: ${smtpConfig.smtp_secure ? 'Yes' : 'No'}</li>
          </ul>
        `,
        text: 'SMTP Test Successful - Your email configuration is working correctly.'
      };

      const emailSent = await this.sendEmailViaSMTP(smtpConfig, testEmail, testEmailContent);

      if (emailSent) {
        return {
          success: true,
          message: 'Test email sent successfully! SMTP configuration is working.'
        };
      } else {
        return {
          success: false,
          message: 'Failed to send test email. Please check your SMTP configuration.',
          error: 'SMTP send failed'
        };
      }
    } catch (error) {
      console.error('Error testing SMTP configuration:', error);
      return {
        success: false,
        message: 'Error testing SMTP configuration.',
        error: (error as Error).message
      };
    }
  }
}

export const passwordResetService = new PasswordResetService();
