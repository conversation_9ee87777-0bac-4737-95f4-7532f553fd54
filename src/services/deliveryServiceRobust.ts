import { supabase } from '../integrations/supabase/client';
import { realTimeService, syncDeliveryStatusUpdate, syncDeliveryCompleted } from './realTimeService';

export interface DeliveryOrder {
  id: string;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  delivery_address: any;
  total: number;
  status: string;
  priority: 'high' | 'medium' | 'low';
  created_at: string;
  delivery_date: string;
  delivery_person_id?: string;
  items_count: number;
  order_items: Array<{
    quantity: number;
    products: {
      title: string;
      featured_image: string;
    };
  }>;
}

export interface DeliveryStats {
  assignedOrders: number;
  completedToday: number;
  inTransit: number;
  totalDistance: number;
  pendingDeliveries: number;
  deliveredThisWeek: number;
  pickedOrders?: number;
  failedDeliveries?: number;
}

class RobustDeliveryService {
  private async checkDatabaseSchema(): Promise<boolean> {
    try {
      // Test if delivery fields exist by attempting a query
      const { error } = await supabase
        .from('orders')
        .select('assigned_delivery_person, delivery_person_name')
        .limit(1);
      
      return !error;
    } catch (error) {
      console.warn('Delivery fields not found in database schema');
      return false;
    }
  }

  /**
   * Get assigned orders for a delivery person with fallback support
   */
  async getAssignedOrders(deliveryPersonId: string): Promise<DeliveryOrder[]> {
    try {
      console.log('🚚 Fetching assigned orders for delivery person:', deliveryPersonId);

      const hasDeliveryFields = await this.checkDatabaseSchema();

      if (!hasDeliveryFields) {
        console.warn('⚠️  Database missing delivery fields - using fallback method');
        return this.getAssignedOrdersFallback(deliveryPersonId);
      }

      // First, let's check what orders exist with this delivery person ID
      console.log('🔍 Checking all orders with assigned_delivery_person field...');
      const { data: allAssignedOrders, error: checkError } = await supabase
        .from('orders')
        .select('id, order_number, status, assigned_delivery_person, delivery_person_name')
        .eq('assigned_delivery_person', deliveryPersonId);

      if (checkError) {
        console.error('❌ Error checking assigned orders:', checkError);
      } else {
        console.log('🔍 All orders assigned to this delivery person:', allAssignedOrders);
        console.log('🔍 Count of all assigned orders (any status):', allAssignedOrders?.length || 0);
      }

      // Now check with status filter
      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          total,
          status,
          created_at,
          delivery_address,
          assigned_delivery_person,
          delivery_person_name,
          users!orders_customer_id_fkey (
            full_name,
            phone
          ),
          order_items (
            quantity,
            products (
              title,
              featured_image
            )
          )
        `)
        .eq('assigned_delivery_person', deliveryPersonId)
        .in('status', ['confirmed', 'preparing', 'ready', 'shipped', 'assigned', 'picked', 'out_for_delivery'])
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching assigned orders:', error);
        console.error('❌ Error details:', JSON.stringify(error, null, 2));
        return this.getAssignedOrdersFallback(deliveryPersonId);
      }

      console.log('✅ Found assigned orders with status filter:', data?.length || 0);
      console.log('📋 Raw order data:', data);

      if (data && data.length > 0) {
        data.forEach((order, index) => {
          console.log(`📦 Order ${index + 1}:`, {
            id: order.id,
            order_number: order.order_number,
            status: order.status,
            assigned_delivery_person: order.assigned_delivery_person,
            delivery_person_name: order.delivery_person_name,
            customer: order.users?.full_name || 'Unknown'
          });
        });
      }

      return this.transformOrderData(data || []);

    } catch (error) {
      console.error('❌ Error in getAssignedOrders:', error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      return this.getAssignedOrdersFallback(deliveryPersonId);
    }
  }

  /**
   * Fallback method when database fields don't exist
   */
  private async getAssignedOrdersFallback(deliveryPersonId: string): Promise<DeliveryOrder[]> {
    try {
      console.log('🔄 Using fallback method - checking all orders for assignment patterns');
      
      // Get all orders and check for delivery assignment in notes or other fields
      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          total,
          status,
          created_at,
          delivery_address,
          notes,
          users!orders_customer_id_fkey (
            full_name,
            phone
          ),
          order_items (
            quantity,
            products (
              title,
              featured_image
            )
          )
        `)
        .in('status', ['confirmed', 'preparing', 'ready', 'shipped', 'assigned', 'picked', 'out_for_delivery'])
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Fallback method failed:', error);
        return [];
      }

      // Filter orders that might be assigned to this delivery person
      // This is a temporary solution until database migration is run
      const potentiallyAssigned = (data || []).filter(order => {
        return order.notes?.includes(deliveryPersonId) || 
               order.status === 'assigned'; // Show all assigned orders as fallback
      });

      console.log('🔍 Fallback found potential orders:', potentiallyAssigned.length);
      return this.transformOrderData(potentiallyAssigned);

    } catch (error) {
      console.error('❌ Fallback method error:', error);
      return [];
    }
  }

  /**
   * Transform raw order data to DeliveryOrder format
   */
  private transformOrderData(orders: any[]): DeliveryOrder[] {
    return orders.map(order => ({
      id: order.id,
      order_number: order.order_number,
      customer_name: order.users?.full_name || 'Unknown Customer',
      customer_phone: order.users?.phone || 'No phone',
      delivery_address: order.delivery_address,
      total: order.total,
      status: order.status,
      priority: 'medium' as const,
      created_at: order.created_at,
      delivery_date: order.created_at,
      delivery_person_id: order.assigned_delivery_person,
      items_count: order.order_items?.reduce((sum, item) => sum + item.quantity, 0) || 0,
      order_items: order.order_items || []
    }));
  }

  /**
   * Update order delivery status with robust error handling
   */
  async updateOrderStatus(orderId: string, newStatus: string, deliveryPersonId: string): Promise<boolean> {
    try {
      console.log('🔄 Updating order status:', { orderId, newStatus, deliveryPersonId });

      const { error } = await supabase
        .from('orders')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString(),
          ...(newStatus === 'delivered' && { delivered_at: new Date().toISOString() })
        })
        .eq('id', orderId);

      if (error) {
        console.error('❌ Error updating order status:', error);
        return false;
      }

      console.log('✅ Order status updated successfully');

      // Emit real-time events for cross-user synchronization
      if (newStatus === 'delivered') {
        syncDeliveryCompleted(orderId, deliveryPersonId, new Date().toISOString());
      } else {
        syncDeliveryStatusUpdate(orderId, newStatus, deliveryPersonId);
      }

      return true;
    } catch (error) {
      console.error('❌ Error in updateOrderStatus:', error);
      return false;
    }
  }

  /**
   * Get delivery history with fallback support
   */
  async getDeliveryHistory(deliveryPersonId: string, limit: number = 20): Promise<DeliveryOrder[]> {
    try {
      console.log('🔄 Fetching delivery history for delivery person:', deliveryPersonId);

      const hasDeliveryFields = await this.checkDatabaseSchema();

      if (!hasDeliveryFields) {
        console.warn('⚠️  Database missing delivery fields - using fallback for history');
        return this.getDeliveryHistoryFallback(deliveryPersonId, limit);
      }

      // Query for completed deliveries using the new delivery_status field
      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          total,
          status,
          delivery_status,
          created_at,
          delivered_at,
          delivery_address,
          assigned_delivery_person,
          delivery_person_name,
          delivery_status_updated_at,
          users!orders_customer_id_fkey (
            full_name,
            phone
          ),
          order_items (
            quantity,
            products (
              title,
              featured_image
            )
          )
        `)
        .eq('assigned_delivery_person', deliveryPersonId)
        .in('delivery_status', ['delivered', 'returned', 'failed'])
        .order('delivery_status_updated_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Error fetching delivery history:', error);
        return this.getDeliveryHistoryFallback(deliveryPersonId, limit);
      }

      console.log('✅ Found delivery history:', data?.length || 0);
      return this.transformOrderData(data || []);
    } catch (error) {
      console.error('❌ Error in getDeliveryHistory:', error);
      return this.getDeliveryHistoryFallback(deliveryPersonId, limit);
    }
  }

  /**
   * Fallback method for delivery history when delivery fields don't exist
   */
  private async getDeliveryHistoryFallback(deliveryPersonId: string, limit: number = 20): Promise<DeliveryOrder[]> {
    try {
      console.log('🔄 Using fallback method for delivery history');

      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          total,
          status,
          created_at,
          delivered_at,
          delivery_address,
          assigned_delivery_person,
          delivery_person_name,
          users!orders_customer_id_fkey (
            full_name,
            phone
          ),
          order_items (
            quantity,
            products (
              title,
              featured_image
            )
          )
        `)
        .eq('assigned_delivery_person', deliveryPersonId)
        .in('status', ['delivered', 'cancelled', 'returned'])
        .order('updated_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('❌ Fallback method failed:', error);
        return [];
      }

      console.log('🔍 Fallback found delivery history:', data?.length || 0);
      return this.transformOrderData(data || []);
    } catch (error) {
      console.error('❌ Error in fallback delivery history:', error);
      return [];
    }
  }

  /**
   * Get delivery statistics with fallback support
   */
  async getDeliveryStats(deliveryPersonId: string): Promise<DeliveryStats> {
    try {
      console.log('🔄 Fetching delivery stats for delivery person:', deliveryPersonId);

      const hasDeliveryFields = await this.checkDatabaseSchema();

      if (!hasDeliveryFields) {
        console.warn('⚠️  Database missing delivery fields - using fallback stats');
        return this.getDeliveryStatsFallback(deliveryPersonId);
      }

      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();

      // Use delivery_status field for accurate statistics
      const [assignedResult, completedTodayResult, inTransitResult, deliveredWeekResult, pickedResult, failedResult] = await Promise.all([
        // Active assigned orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).in('delivery_status', ['assigned', 'picked', 'out_for_delivery']),
        // Completed today
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('delivery_status', 'delivered').gte('delivery_status_updated_at', today),
        // Currently in transit
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('delivery_status', 'out_for_delivery'),
        // Delivered this week
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('delivery_status', 'delivered').gte('delivery_status_updated_at', weekAgo),
        // Picked up orders
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('delivery_status', 'picked'),
        // Failed deliveries
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).in('delivery_status', ['failed', 'returned'])
      ]);

      const stats = {
        assignedOrders: assignedResult.count || 0,
        completedToday: completedTodayResult.count || 0,
        inTransit: inTransitResult.count || 0,
        totalDistance: 0, // This would need GPS tracking to calculate
        pendingDeliveries: (assignedResult.count || 0) - (inTransitResult.count || 0),
        deliveredThisWeek: deliveredWeekResult.count || 0,
        pickedOrders: pickedResult.count || 0,
        failedDeliveries: failedResult.count || 0
      };

      console.log('✅ Delivery stats calculated:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Error fetching delivery stats:', error);
      return this.getDeliveryStatsFallback(deliveryPersonId);
    }
  }

  /**
   * Fallback method for delivery stats when delivery fields don't exist
   */
  private async getDeliveryStatsFallback(deliveryPersonId: string): Promise<DeliveryStats> {
    try {
      console.log('🔄 Using fallback method for delivery stats');

      const today = new Date().toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();

      const [assignedResult, completedTodayResult, inTransitResult, deliveredWeekResult] = await Promise.all([
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).in('status', ['confirmed', 'preparing', 'ready', 'shipped']),
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('status', 'delivered').gte('updated_at', today),
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('status', 'shipped'),
        supabase.from('orders').select('id', { count: 'exact' }).eq('assigned_delivery_person', deliveryPersonId).eq('status', 'delivered').gte('updated_at', weekAgo)
      ]);

      const fallbackStats = {
        assignedOrders: assignedResult.count || 0,
        completedToday: completedTodayResult.count || 0,
        inTransit: inTransitResult.count || 0,
        totalDistance: 0,
        pendingDeliveries: assignedResult.count || 0,
        deliveredThisWeek: deliveredWeekResult.count || 0
      };

      console.log('🔍 Fallback stats calculated:', fallbackStats);
      return fallbackStats;
    } catch (error) {
      console.error('❌ Error in fallback delivery stats:', error);
      return {
        assignedOrders: 0,
        completedToday: 0,
        inTransit: 0,
        totalDistance: 0,
        pendingDeliveries: 0,
        deliveredThisWeek: 0
      };
    }
  }

  /**
   * Subscribe to real-time order updates
   */
  subscribeToOrderUpdates(deliveryPersonId: string, callback: (payload: any) => void) {
    return supabase
      .channel('delivery-orders')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'orders' }, callback)
      .subscribe();
  }

  /**
   * Make a phone call (opens phone app)
   */
  makePhoneCall(phoneNumber: string) {
    if (phoneNumber) {
      window.open(`tel:${phoneNumber}`, '_self');
    }
  }

  /**
   * Open navigation to address
   */
  navigateToAddress(address: string) {
    if (address) {
      const encodedAddress = encodeURIComponent(typeof address === 'string' ? address : JSON.stringify(address));
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      
      if (isIOS) {
        window.open(`maps://maps.google.com/maps?daddr=${encodedAddress}`, '_blank');
      } else {
        window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');
      }
    }
  }
}

export const robustDeliveryService = new RobustDeliveryService();
export default robustDeliveryService;
