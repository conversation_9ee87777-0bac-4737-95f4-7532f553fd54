/**
 * New AuthContext with better error handling
 * This fixes the "dispatcher is null" error
 */

import React from 'react';
import { User } from '../types/user';
import { supabase } from '@/integrations/supabase/client';

// Function to update last login timestamp
const updateLastLogin = async (userId: string) => {
  try {
    const { error } = await supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId);

    if (error) {
      console.error('Error updating last login:', error);
    } else {
      console.log('Last login updated successfully for user:', userId);
    }
  } catch (error) {
    console.error('Error in updateLastLogin:', error);
  }
};

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (userData: User) => void;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
  hasPermission: (permission: string) => boolean;
  sessionExpiry: Date | null;
}

// Create context with default values
const AuthContext = React.createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  loading: true,
  login: () => {},
  logout: () => {},
  updateUser: () => {},
  hasPermission: () => false,
  sessionExpiry: null,
});

// Default permissions for different user types
const defaultPermissions = {
  admin: [
    'users.create', 'users.read', 'users.update', 'users.delete',
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete',
    'reports.read', 'settings.write', 'branches.manage'
  ],
  manager: [
    'products.create', 'products.read', 'products.update', 'products.delete',
    'orders.create', 'orders.read', 'orders.update', 'orders.delete',
    'inventory.read', 'inventory.update', 'inventory.manage',
    'categories.create', 'categories.read', 'categories.update', 'categories.delete',
    'branches.read', 'branches.update', 'branches.manage',
    'clients.read', 'clients.update', 'clients.manage',
    'promocodes.create', 'promocodes.read', 'promocodes.update', 'promocodes.delete',
    'reviews.read', 'reviews.update', 'reviews.manage',
    'profile.read', 'profile.write'
  ],
  client: [
    'products.read', 'orders.create', 'orders.read', 'orders.bulk',
    'profile.read', 'profile.write', 'wholesale.read'
  ]
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use React.useState to avoid dispatcher issues
  const [user, setUser] = React.useState<User | null>(null);
  const [sessionExpiry, setSessionExpiry] = React.useState<Date | null>(null);
  const [loading, setLoading] = React.useState(true);

  // Initialize auth state
  React.useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      try {
        // Check for existing Supabase session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Session check error:', error);
          if (isMounted) {
            setLoading(false);
          }
          return;
        }

        if (session?.user && isMounted) {
          // Try to get user profile from database
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError) {
            console.warn('Profile not found, using auth metadata');
            // Create user from auth metadata
            const authUser: User = {
              id: session.user.id,
              email: session.user.email || '',
              fullName: session.user.user_metadata?.full_name || session.user.email || 'User',
              userType: session.user.user_metadata?.user_type || 'client',
              isActive: true,
              createdAt: session.user.created_at,
              updatedAt: new Date().toISOString(),
              phone: session.user.user_metadata?.phone || '',
              city: session.user.user_metadata?.city || 'Tetouan',
              permissions: defaultPermissions[session.user.user_metadata?.user_type as keyof typeof defaultPermissions] || defaultPermissions.client
            };
            setUser(authUser);
          } else {
            // Use database profile
            const dbUser: User = {
              id: userProfile.id,
              email: userProfile.email,
              fullName: userProfile.full_name,
              userType: userProfile.user_type,
              isActive: userProfile.is_active,
              createdAt: userProfile.created_at,
              updatedAt: userProfile.updated_at,
              phone: userProfile.phone || '',
              city: userProfile.city || 'Tetouan',
              companyName: userProfile.company_name,
              companyAddress: userProfile.company_address,
              permissions: defaultPermissions[userProfile.user_type as keyof typeof defaultPermissions] || defaultPermissions.client
            };
            setUser(dbUser);
          }

          // Set session expiry
          if (session.expires_at) {
            setSessionExpiry(new Date(session.expires_at * 1000));
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!isMounted) return;

        console.log('Auth state changed:', event);
        
        if (event === 'SIGNED_OUT' || !session) {
          setUser(null);
          setSessionExpiry(null);
        } else if (event === 'SIGNED_IN' && session) {
          // Reinitialize user data
          initializeAuth();
        }
      }
    );

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const login = React.useCallback((userData: User) => {
    setUser(userData);

    // Set session expiry (24 hours from now)
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 24);
    setSessionExpiry(expiry);

    // Update last login timestamp
    updateLastLogin(userData.id);
  }, []);

  const logout = React.useCallback(async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setSessionExpiry(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if Supabase fails
      setUser(null);
      setSessionExpiry(null);
    }
  }, []);

  const updateUser = React.useCallback((userData: Partial<User>) => {
    setUser(prev => prev ? { ...prev, ...userData } : null);
  }, []);

  const hasPermission = React.useCallback((permission: string): boolean => {
    if (!user) return false;
    return user.permissions?.includes(permission) || false;
  }, [user]);

  const isAuthenticated = React.useMemo(() => {
    return user !== null && user.isActive;
  }, [user]);

  const contextValue = React.useMemo(() => ({
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    updateUser,
    hasPermission,
    sessionExpiry,
  }), [user, isAuthenticated, loading, login, logout, updateUser, hasPermission, sessionExpiry]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
